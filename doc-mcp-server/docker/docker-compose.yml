version: '3.8'

services:
  bkui-doc-server:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - BKUI_COMPONENTS_PATH=/app/components
      - PORT=3000
    volumes:
      # 挂载组件文档目录
      - ../example/components:/app/components:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/mcp"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
