#!/bin/bash

# B<PERSON><PERSON> Vue2 Documentation MCP Server 安装脚本

echo "🚀 Installing BKUI Vue2 Documentation MCP Server..."

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version must be 18 or higher. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# 安装依赖
echo "📦 Installing dependencies..."
npm install

# 构建项目
echo "🔨 Building project..."
npm run build

# 复制环境变量文件
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ Created .env file. Please edit it to configure your components path."
fi

echo "✅ Installation completed!"
echo ""
echo "📋 Available commands:"
echo "  npm run dev     - Start development server"
echo "  npm start       - Start HTTP server"
echo "  npm run stdio   - Start stdio server (for MCP clients)"
echo "  npm test        - Run tests"
echo ""
echo "🔧 Configuration:"
echo "  Edit .env file to set BKUI_COMPONENTS_PATH"
echo "  Default path: ../example/components"
