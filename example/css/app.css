/* @mixin scroller($backgroundColor: #e6e9ea, $width: 4px) {
    &::-webkit-scrollbar {
        width: $width;
        background-color: lighten($backgroundColor, 80%);
    }
    &::-webkit-scrollbar-thumb {
        height: 5px;
        border-radius: 2px;
        background-color: $backgroundColor;
    }
} */

@import './mixins/scroller.css';
@import './mixins/ellipsis.css';

.mac {
    font-family: PingFang SC, Microsoft Yahei, Helvetica, Aria;
}

.win {
    font-family: Microsoft Yahei, PingFang SC, Helvetica, Aria;
}
body {
    color: #63656e;
    /* @mixin scroller red, 100px; */
    @mixin scroller;
}

/* .mg-header {
    z-index: 2000!important;
} */

.app-container {
    width: 100%;
    margin-top: 90px;
    /* overflow: hidden; */

    .app-side-nav {
        width: 250px;
        border-right: 1px solid #eee;
        position: fixed;
        bottom: 0;
        top: 62px;
        overflow: scroll;
        margin-bottom: 0;
        z-index: 1;
        background-color: #fff;
        padding-bottom: 80px;
        /* padding-top: 10px; */
        overflow-x: hidden;
        @mixin scroller;
    }

    .app-content {
        width: calc(100% - 250px);
        min-width: 950px;
        margin-left: 250px;
        padding: 0 195px 0 30px;
        margin-bottom: 50px;
        position: relative;
        min-height: 700px;
        /* transform: scale3d(1, 1, 1); */

        .toggle-lang-wrapper {
            position: absolute;
            top: 4px;
            display: none;
        }
        .back-icon {
            display: inline-block;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            position: relative;
            top: 1px;
        }
        .github-link {
            width: 24px;
            height: 24px;
            position: absolute;
            top: 5px;
            margin-left: 10px;
            cursor: pointer;
        }
    }

    .table-of-contents {
        position: fixed;
        transform: translateY(0);
        right: 0;
        font-size: 12px;
        padding-right: 20px;
        ul {
            margin: 0;
            list-style: none;
            padding-left: 13px;
            li {
                @mixin ellipsis 140px, block;
                line-height: 25px;
                border-left: 1px solid #dcdee5;
                margin-left: -14px;
                padding-left: 13px;
                width: 140px;
                &.cur {
                    color: #3a84ff;
                    border-left: 1px solid #3a84ff;
                    a {
                        color: #3a84ff;
                    }
                }
                a {
                    color: #63656e;
                    &:hover {
                        color: #3a84ff;
                    }
                }
            }
        }
    }

    .color-wrapper {
        margin-bottom: 15px;
        height: 180px;
        .color-item {
            border-radius: 2px;
            width: 320px;
            height: 160px;
            margin-right: 20px;
            color: #fff;
            font-size: 14px;
            position: relative;
            float: left;
            margin-bottom: 60px;
            &:last-child {
                margin-right: 0px;
            }
            .main-color {
                height: 100%;
                padding: 10px;
                span {
                    display: inline-block;
                    width: 100%;
                    text-align: center;
                    position: absolute;
                    bottom: 78px;
                    left: 0;
                    font-weight: 700;
                    font-size: 28px;
                }
            }
            .other-color {
                display: flex;
                position: absolute;
                bottom: 0;
                width: 100%;
                div {
                    padding: 10px;
                    flex: 1;
                    width: 33.33%;
                    height: 40px;
                    font-size: 12px;
                    span {
                        position: absolute;
                        bottom: 6px;
                    }
                }
            }
            .color-tips {
                margin-top: 15px;
                color: #63656e;
                font-size: 14px;
            }
        }
        .color-item-max {
            width: 360px;
            .other-color {
                div {
                    width: 25%;
                }
            }
        }
    }

    .font-wrapper {
        /* margin-bottom: 15px; */
        margin-bottom: 210px;
        height: 140px;
        .font-item {
            width: 312px;
            height: 140px;
            margin-right: 12px;
            color: #fff;
            font-size: 14px;
            position: relative;
            border: 1px solid #dcdee5;
            float: left;
            margin-bottom: 30px;
            .main {
                height: 98px;
                color: #313238;
                line-height: 98px;
                padding: 0 15px;
                position: relative;
                .f32 {
                    position: absolute;
                    transform: translate(-50%, -50%);
                    left: 50%;
                    top: 50%;
                }
                .f24,
                .f18,
                .f16,
                .f14,
                .f12 {
                    position: absolute;
                    transform: translateY(-50%);
                    top: 50%;
                }
                p {
                    line-height: 1.2;
                    color: #313238;
                    padding: 0;
                    margin: 0;
                }
            }
            .info {
                height: 40px;
                background-color: #fafbfd;
                border-top: 1px solid #dcdee5;
                color: #63656e;
                padding: 0 15px;
                span {
                    display: inline-block;
                    height: 40px;
                    line-height: 40px;
                    &:last-child {
                        float: right;
                    }
                }
            }
        }
    }

    .color-spec {
        font-size: 14px;
        color: #63656e;
        line-height: 1.8;
    }

    a {
        color: #3c96ff;
        text-decoration: none;
        cursor: pointer;
    }

    .toggle-lang {
        display: inline-block;
        background-color: #fafafa;
        color: #c3cdd7;
        font-size: 12px;
        border: 1px solid #c3cdd7;
        vertical-align: middle;
        box-sizing: border-box;
        overflow: hidden;
        border-radius: 2px;
        white-space: nowrap;
        padding: 0 9px;
        height: 21px;
        line-height: 19px;
        margin-left: 10px;
    }

    .example-link {
        margin-bottom: -1.5em;
    }

    .example-item {
        padding: 0 0 10px;
        width: 100%;
        min-height: 80px;
        & ~ .example-item {
            margin-top: 20px;
        }
        .inner {
            float: left;
            display: inline-block;
            margin-right: 20px;
        }
    }

    code {
        font-family: 'Microsoft Yahei', arial;
        background-color: #f9fafc;
        /* border: 1px solid #eaeefb; */
        padding: 0 5px;
        /* margin: 0 2px; */
    }

    h2,
    h3,
    h4,
    h5 {
        font-weight: 400;
        color: #1f2f3d;
        &:hover {
            .header-anchor {
                visibility: visible;
            }
        }
    }

    p {
        font-size: 14px;
        color: #5e6d82;
        line-height: 1.5em;
    }

    h3 {
        margin: 45px 0 15px;
    }

    .demo-box {
        border: 1px solid #f0f0f0;
        position: relative;
    }

    .demo-box .demo-desc code {
        background-color: #f1f1f1;
        border: 1px solid #eaeefb;
        border-radius: 4px;
        transition: .2s;
        display: inline-block;
        padding: 0 4px;
        margin: 5px;
    }

    .header-anchor {
        float: left;
        margin-left: -20px;
        padding-right: 4px;
        visibility: hidden;
        padding-top: 1px;
    }

    .demo-box .demo-source {
        padding: 24px;
    }

    .demo-box .demo-meta {
        background-color: #f9fafc;
        border-top: 1px solid #eaeefb;
        overflow: hidden;
        transition: height .2s;
        display: flex;
        max-height: 300px;

        &:before,
        &:after {
            content: '';
            display: table;
            line-height: 0;
        }

        &:after {
            clear: both;
        }
    }

   .demo-box .demo-code {
        background-color: #f9fafc;
        border-top: 1px solid #eaeefb;
        transition: height .2s;
        display: flex;

        &:before,
        &:after {
            content: '';
            display: table;
            line-height: 0;
        }

        &:after {
            clear: both;
        }
    }

    .demo-box .demo-code > pre {
        display: block;
        overflow: auto;
        margin: 0;
        padding: 10px;
        font-family: "微软雅黑", arial;

        &::-webkit-scrollbar {
            width: 6px;
            height: 5px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 20px;
            background: #a5a5a5;
            -webkit-box-shadow: inset 0 0 6px hsla(0, 0%, 80%, .3);
        }
    }

    .demo-box .demo-meta > pre {
        display: block;
        width: 60%;
        border-left: 1px solid #eaeefb;
        overflow: auto;
        margin: 0;
        padding: 24px;
        font-family: "微软雅黑", arial;

        &::-webkit-scrollbar {
            width: 6px;
            height: 5px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 20px;
            background: #a5a5a5;
            -webkit-box-shadow: inset 0 0 6px hsla(0, 0%, 80%, .3);
        }
    }

    .demo-box .demo-meta > pre code {
        font-size: 12px;
        display: block;
        line-height: 1.5;
        padding: 0;
        background-color: #f9fafc;
        border: none;
    }

    .demo-box .demo-code > pre code {
        font-size: 12px;
        display: block;
        line-height: 1.8;
        padding: 0;
        background-color: #f9fafc;
        border: none;
    }

    .demo-meta {
        .demo-source-code {
            position: absolute;
            top: 32px;
            left: 32px;
        }
    }

    .demo-box .demo-desc {
        padding: 18px 24px;
        width: 40%;
        box-sizing: border-box;
        float: right;
        font-size: 14px;
        line-height: 1.8;
        color: #5e6d82;
        word-break: break-word;
        p {
            margin: 0;
            line-height: 1.5;
            font-size: 13px;
        }
    }
    .demo-box .code-copy {
        position: absolute;
        right: 25px;
        margin-top: 10px;
        cursor: pointer;
        z-index: 1;
    }

    .row-auto-height {
        .demo-box {
            .demo-meta {
                max-height: max-content;
            }
        }
    }

    .table {
        border-collapse: collapse;
        width: 100%;
        background-color: #fff;
        color: #5e6d82;
        font-size: 14px;
        margin-bottom: 45px;
        line-height: 1.5em;
    }

    .table th,
    .table td {
        padding: 0;
    }

    .table td:first-child,
    .table th:first-child {
        padding-left: 10px;
    }

    .table td,
    .table th {
        border-bottom: 1px solid #eaeefb;
        padding: 10px;
        max-width: 250px;
    }

    .table th {
        text-align: left;
        border-top: 1px solid #eaeefb;
        background-color: #eff2f7;
        white-space: nowrap;
    }

    .language-html,
    .language-css,
    .language-javascript,
    .language-bash,
    .language-js {
        display: block;
        color: #888;
        line-height: 1.8;
        font-family: Menlo,Monaco,Consolas,Courier,monospace;
        font-size: 12px;
        padding: 15px;
        background-color: #f9fafc;
    }

    .hljs {
        display: block;
        overflow-x: auto;
        padding: 15px;
        background: #f9fafc;
    }

    .hljs,
    .hljs-subst {
        /* color: #000; */
    }

    .hljs-addition,
    .hljs-meta,
    .hljs-string,
    .hljs-symbol,
    .hljs-template-tag,
    .hljs-template-variable {
        color: #756bb1
    }

    .hljs-comment,
    .hljs-quote {
        color: #636363
    }

    .hljs-bullet,
    .hljs-link,
    .hljs-literal,
    .hljs-number,
    .hljs-regexp {
        color: #31a354
    }

    .hljs-deletion,
    .hljs-variable {
        color: #88f
    }

    .hljs-built_in,
    .hljs-doctag,
    .hljs-keyword,
    .hljs-name,
    .hljs-section,
    .hljs-selector-class,
    .hljs-selector-id,
    .hljs-selector-tag,
    .hljs-strong,
    .hljs-tag,
    .hljs-title,
    .hljs-type {
        color: #3182bd
    }

    .hljs-emphasis {
        font-style: italic
    }

    .hljs-attribute {
        color: #e6550d
    }

    .changelog-wrapper {
        min-height: 600px;
        ul {
            padding: 10px 30px 15px 20px;
            li {
                font-size: 14px;
                list-style: none;
                padding-left: 20px;
                padding-bottom: 5px;
                color: #333;
                word-break: break-all;
                &:before {
                    content: "";
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: #333;
                    transform: translateX(-15px);
                    display: inline-block;
                    vertical-align: middle;
                }
            }
        }
        hr {
            background: #c3cdd7;
            width: 100%;
            height: 1px;
            border: none;
        }
        em {
            font-size: 12px;
            margin-left: 15px;
        }
        h6 {
            margin: 0;
            margin-bottom: -10px;
            font-style: italic;
        }
    }

    .tips-block {
        position: relative;
        &.with-title {
            &:before {
                content: "!";
                position: absolute;
                width: 19px;
                height: 19px;
                left: -11px;
                top: 13px;
                text-align: center;
                line-height: 21px;
                border-radius: 50%;
                color: #fff;
                font-size: 14px;
            }
        }
        &.tip,
        &.info,
        &.warning,
        &.danger {
            padding: 1px 15px;
            margin: 12px 0;
            border-left: 3px solid;
            .hljs {
                background-color: transparent;
                padding: 0;
            }
        }
        &.tip {
            background-color: #f3f5f7;
            border-color: #42b983;
            &:before {
                background-color: #42b983;
            }
        }
        &.info {
            background-color: rgba(225, 236, 255, 0.5);
            border-color: #699df4;
            &:before {
                background-color: #699df4;
            }
        }
        &.warning {
            background-color: rgba(255, 229, 100, 0.3);
            border-color: #b19300;
            color: #000;
            &:before {
                background-color: #b19300;
            }
        }
        &.danger {
            background-color: #ffe6e6;
            border-color: #f66;
            color: #000;
            &:before {
                background-color: #f66;
            }
        }
    }
}

@media screen and (max-width: 1478px) {
    .app-container .help-color {
        margin-bottom: 270px;
    }
}

@media screen and (max-width: 1219px) {
    .app-container .middle-color {
        margin-bottom: 270px;
    }
}

@media screen and (max-width: 1450px) {
    .app-container .font-wrapper {
        margin-bottom: 380px;
    }
}
