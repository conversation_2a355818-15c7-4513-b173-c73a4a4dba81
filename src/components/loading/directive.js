/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
*/

/**
 * @file loading directive
 *
 * Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved. 蓝鲸智云 版权所有
 */

import Vue from 'vue'
import ViewModel from './loading.vue'

const Model = Vue.extend(ViewModel)

function show (el, options) {
  if (!el.$vm) {
    el.$vm = el.viewmodel.$mount()
    el.appendChild(el.$vm.$el)
  }
  el.$vm.duration = 0
  Vue.nextTick(() => {
    el.$vm.isShow = true
  })
}

function toggle (el, options) {
  if (!el.$vm) {
    el.$vm = el.viewmodel.$mount()
    el.appendChild(el.$vm.$el)
  }

  clearTimeout(el.$vm.timer)

  if (options.isLoading) {
    Vue.nextTick(() => {
      el.$vm.isShow = true
    })
  } else {
    const delay = isNaN(options.delay) ? 0 : Number(options.delay)
    if (delay > 0) {
      el.$vm.timer = setTimeout(() => {
        el.$vm.isShow = false
      }, delay)
    } else {
      Vue.nextTick(() => {
        el.$vm.isShow = false
      })
    }
  }

  if (options.title) {
    el.$vm.title = options.title
  }
  if (options.size) {
    el.$vm.size = options.size
  }
}

const bkLoading = {
  inserted (el, binding) {
    const value = binding.value

    const position = getComputedStyle(el).position
    const options = {}

    if (!position || position !== 'relative' || position !== 'absolute') {
      el.style.position = 'relative'
    }

    for (const key in value) {
      if (key !== 'isLoading') {
        options[key] = value[key]
      }
    }

    options.delay = 3000
    options.type = 'absolute'
    options.opacity = options.opacity || 0.9
    options.color = options.color || '#ffffff'

    el.viewmodel = new Model({
      propsData: options
    })

    // 在第一次渲染时，immediate为true立即显示
    if (options.immediate) {
      show(el, binding.value)
    } else {
      toggle(el, binding.value)
    }
  },
  update (el, binding) {
    if (el.viewmodel) {
      toggle(el, binding.value)
    }
  }
}

export default bkLoading
