@import './variable.css';
@import './mixins/ellipsis.css';

:root {
    --font-size: 12px;

    .medium-font {
        --font-size: 14px;
    }

    .large-font {
        --font-size: 16px;
    }
}

.bk-form-control {
    position: relative;
    display: inline-block;
    width: 100%;
    display: block;
    color: $newBlackColor2;

    .bk-limit-box {
        position: absolute;
        bottom: 2px;
        right: 7px;
        font-size: 12px;
        margin: 0;
        padding: 0;
        color: $newBlackColor3;
        transform: scale(0.8, 0.8);
        .strong {
            color: $newBlackColor2;
        }
    }

     &.control-active {
        /*textarea是在wrapper层实现边框*/
        .bk-textarea-wrapper {
            border-color: $newMainColor !important;
            background-color: #fff !important;
            &::placeholder {
                color: $newGreyColor !important;
            }
        }
    }

    &.control-disable {
        .bk-textarea-wrapper {
            background-color: $newGreyColor3;
            cursor: not-allowed;
        }
    }

    &.control-prepend-group {
        display: flex;
        background-color: #f2f4f8;

        .bk-form-input,
        .bk-form-password,
        .bk-form-select,
        .bk-form-textarea {
            border-radius: 0 2px 2px 0;
        }
    }
    &.control-append-group {
        display: flex;
        .bk-form-input,
        .bk-form-password,
        .bk-form-select,
        .bk-form-textarea {
            border-radius: 2px 0 0 2px;
        }
    }
    &.control-prepend-group.control-append-group {
        .bk-form-input,
        .bk-form-password,
        .bk-form-select,
        .bk-form-textarea {
            border-radius: 0;
        }
    }
    .group-box {
        vertical-align: middle;
        position: relative;
        border: 1px solid $newGreyColor;
        border-radius: 2px;
        height: 32px;

        .group-text {
            @mixin ellipsis 200px;
            color: $newBlackColor2;
            padding: 0 15px;
            font-size: var(--font-size);
            vertical-align: middle;
            line-height: 28px;
            &.bk-dropdown-menu {
                padding: 0;
                overflow: visible;
                white-space: normal;
                .bk-button {
                    @mixin ellipsis 200px;
                    border: none;
                    border-radius: 0;
                    height: 30px;
                    padding-right: 30px;
                    .bk-icon {
                        position: absolute;
                        right: 10px;
                        top: 7px;
                    }
                }
            }
        }
        &.group-prepend {
            border-right: none;
            border-radius: 2px 0 0 2px;
        }
        &.group-append {
            border-left: none;
            border-radius: 0 2px 2px 0;
        }
    }
    .control-icon {
        display: flex;
        align-items: center;
        position: absolute;
        font-size: $iconSmallerSize;
        top: 50%;
        transform: translateY(-50%);
        color: $newGreyColor;
        .clear-icon {
            cursor: pointer;
            font-size: $iconSmallestSize;
            &:hover {
                color: $newBlackColor3;
            }
        }
        &.left-icon {
            left: 10px;
        }
    }
    &.with-left-icon {
        .bk-form-input,
        .bk-form-password,
        .bk-form-select,
        .bk-form-textarea {
            padding-left: 30px;
        }
    }
    &.with-right-icon {
        .bk-form-input,
        .bk-form-password,
        .bk-form-select,
        .bk-form-textarea {
            padding-right: 30px;
        }

        .bk-input-number {
            position: relative;
            input {
                padding-right: 12px;
            }
        }
    }

    .input-right-icon {
        cursor: pointer;
        &:hover {
            color: $primaryColor;
        }
    }
}

.bk-form-input,
.bk-form-password,
.bk-form-select,
.bk-form-textarea {
    box-sizing: border-box;
    height: 32px;
    line-height: normal;
    color: $newBlackColor2;
    background-color: #fff;
    border-radius: 2px;
    width: 100%;
    font-size: var(--font-size);
    box-sizing: border-box;
    border: 1px solid $newGreyColor;
    padding: 0 10px 0 8px;
    text-align: left;
    vertical-align: middle;
    outline: none;
    resize:none;
    transition: border linear .2s;
    &.only-bottom-border {
        border-color: transparent transparent $newGreyColor transparent;
        &:focus {
            border-color: transparent transparent $newMainColor transparent !important;
            box-shadow: none;
        }
        &[disabled],
        &[readonly] {
            border-color: transparent transparent $newGreyColor1 transparent !important;
        }
    }
    &:focus {
        border-color: $newMainColor !important;
        background-color: #fff !important;
        &::placeholder {
            color: $newGreyColor !important;
        }
    }
    &::placeholder {
        color: $newGreyColor;
    }

    &[readonly] {
        background-color: $newGreyColor3 !important;
        cursor: default;
        border-color: $newGreyColor1 !important;
    }
    &[disabled] {
        /* color: $formBorderColor; */
        background-color: $newGreyColor3 !important;
        cursor: not-allowed;
        border-color: $newGreyColor1 !important;
    }
}

.bk-input-text,
.bk-input-textarea,
.bk-input-password,
.bk-input-number,
.bk-input-email,
.bk-input-url,
.bk-input-date {
    width: 100%;
}

.bk-input-number {
    position: relative;
    input {
        padding-right: 12px;
        &[type="number"] {
            &::-webkit-outer-spin-button,&::-webkit-inner-spin-button {
                appearance: none;
            }
            -moz-appearance: textfield;
        }
    }
    .input-number-option {
        position: absolute;
        right: 2px;
        top: 1px;
        bottom: 1px;
        width: 26px;
        text-align: center;
        display: flex;
        justify-content: center;
        flex-direction: column;
        background: #fff;

        .number-option-item {
            flex: auto;
            width: 100%;
            max-height: 45%;
            color: #979ba5;
            font-size: $iconSmallSize;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover {
                background: rgba(244, 246, 250, 1);
                cursor: pointer;
            }
        }
    }
}

.bk-textarea-wrapper {
    border: 1px solid $newGreyColor;
    border-radius: 2px;
    .bk-form-textarea {
        border: none;
        height: auto;
        &.textarea-maxlength {
            margin-bottom: 16px;
        }
    }
}

.bk-form-textarea {
    min-height: 70px;
    line-height: 20px;
    padding: 5px 10px;
    line-height: 1.5;
}

/* size */
.bk-input-wrapper-small {
    .group-box {
        height: 26px;
        .group-text {
            font-size: 12px;
            line-height: 24px;
            &.bk-dropdown-menu {
                line-height: unset;
                .bk-button {
                    height: 24px;
                    line-height: 24px;
                    .bk-icon {
                        top: 4px;
                    }
                }
            }
        }
    }
    .input-number-option {
        .number-option-item {
            font-size: $iconSmallestSize;
        }
    }
}
.bk-input-wrapper-large {
    .group-box {
        height: 38px;
        .group-text {
            font-size: 14px;
            line-height: 36px;
            &.bk-dropdown-menu {
                line-height: unset;
                .bk-button {
                    height: 36px;
                    line-height: 36px;
                    .bk-icon {
                        top: 10px;
                    }
                }
            }
        }
    }
    .input-number-option {
        .number-option-item {
            font-size: $iconNormalSize;
        }
    }
}
.bk-input-small {
    height: 26px;
    font-size: 12px;
    line-height: 26px;
}
.bk-input-large {
    height: 38px;
    font-size: 14px;
    line-height: 38px;
}
.large-font,
.medium-font {
    .bk-input-large,
    .bk-input-small,
    .group-text {
        font-size: var(--font-size) !important;
    }
}
