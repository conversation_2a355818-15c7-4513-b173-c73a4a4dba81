.bk-compose-form-item {
    display: inline-block;

    & > .bk-form-control,
    & > .bk-select,
    & > .bk-cascade,
    & > .bk-color-picker,
    & > .bk-date-picker,
    & > .bk-tag-input,
    & > .bk-tag-selector,
    & > .search-select-wrap {
        float: left;
        width: auto;
        margin-left: -1px;
    }

    .bk-form-input,
    .bk-form-password,
    .bk-form-textarea,
    .bk-select,
    .bk-cascade,
    .bk-color-picker,
    .bk-date-picker .bk-date-picker-editor,
    .bk-tag-input,
    .bk-search-select {
        border-radius: 0;
    }

    .bk-form-control.control-active,
    .bk-select.is-focus,
    .bk-cascade.is-focus,
    .bk-color-picker.bk-color-picker-show-dropdown,
    .bk-color-picker:hover,
    .bk-date-picker .bk-date-picker-editor:focus,
    .bk-tag-input.active,
    .bk-tag-selector,
    .bk-search-select.is-focus {
        opacity: 1;
        z-index: 1;
    }

    .bk-compose-form-item-head {
        .bk-form-input,
        .bk-form-password,
        .bk-form-textarea,
        &.bk-select,
        &.bk-cascade,
        &.bk-color-picker,
        &.bk-date-picker .bk-date-picker-editor,
        &.bk-tag-input,
        .bk-tag-input,
        .bk-search-select {
            margin-left: 0;
            border-top-right-radius: 0px;
            border-bottom-right-radius: 0px;
        }
    }
    .bk-compose-form-item-tail {
        .bk-form-input,
        .bk-form-password,
        .bk-form-textarea,
        &.bk-select,
        &.bk-cascade,
        &.bk-color-picker,
        &.bk-date-picker .bk-date-picker-editor,
        &.bk-tag-input,
        .bk-tag-input,
        .bk-search-select {
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
        }
    }
}
