@import './variable.css';

.bk-collapse {
    color: $fnMainColor;
    .bk-collapse-header {
        padding: 10px;
        font-size: $fnNormalSize;
        i.bk-icon {
            transition: all linear 0.3s;
        }
        .rotate-icon {
            position: relative;
            transform: rotate(90deg);
        }
    }

    .bk-collapse-content {
        padding: 5px 10px 20px 10px;
        will-change: all;
        font-size: $fnNormalSize;
        color: $fnMainColor;
    }
}
