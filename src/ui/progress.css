@import './variable.css';

.bk-progress {
    display: flex;
    align-items: center;
    .progress-bar {
        width: 100%;
        height: 4px;
        background: #F0F1F5;
        border-radius:50px;
        position: relative;
        vertical-align: middle;
        text-align: center;
        &.bk-progress-small {
            height: 4px;
        }
        &.bk-progress-normal {
            height: 8px;
        }
        &.bk-progress-large {
            height: 12px;
        }
    }
    .progress-inner {
        position: absolute;
        left: 0;
        top: 0;
        width: 50%;
        height: 100%;
        border-radius: 50px;
        transition: width 0.5s ease;
        &.bk-primary {
            background: $newMainColor;
        }
        &.bk-success {
            background: $newGreenColor;
        }
        &.bk-warning {
            background: $newOrangeColor;
        }
        &.bk-danger {
            background: $dangerColor;
        }
    }
    .inner-text {
        width: 100%;
        text-align: center;
        color: #fff;
        height: 100%;
    },
    .progress-text {
        width: 7%;
        text-align: center;
    }
}
