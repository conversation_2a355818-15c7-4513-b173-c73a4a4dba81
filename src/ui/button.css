@import './variable.css';
@import './mixins/create-button.css';
@import './mixins/create-button-hover.css';

.bk-button {
    height: 32px;
    line-height: 30px;
    display: inline-block;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    -webkit-appearance: none;
    padding: 0 15px;
    text-align: center;
    vertical-align: middle;
    font-size: $fnNormalSize;
    background-color: $defaultColor;
    border: 1px solid $newGreyColor;
    border-radius: 2px;
    box-sizing: border-box;
    color: $newBlackColor2;
    text-decoration: none;
    transition: background-color ease 0.3s;
    min-width: 64px;
    position: relative;

    .bk-loading-wrapper {
        opacity: 0;
    }

    .bk-button-loading {
        display: inline-block;
        vertical-align: top;
        font-size: 0;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        div {
            width: 6px;
            height: 6px;
            border-radius: 100%;
            display: inline-block;
            vertical-align: middle;
            background-color: $defaultColor;
            animation: button-loading 1s infinite ease-in-out both;
            & + [class^="bounce"] {
                margin-left: 4px;
            }
        }
        .bounce1 {
            animation-delay: -0.72s;
        }
        .bounce2 {
            animation-delay: -0.48s;
        }
        .bounce3 {
            animation-delay: -0.24s;
        }
        .bounce4 {
            animation-delay: 0s;
        }
    }

    .bk-button-icon-loading {
        position: relative;
        top: 2px;
        .loading {
            width: 14px;
            height: 14px;
            border: 2px solid;
            border-right-color: transparent;
            border-radius: 50%;
            animation: button-icon-loading 1s linear infinite;
            margin: -7px auto 0;
            display: inline-block;
        }
    }

    .bk-icon {
        line-height: 16px;
        min-width: 14px;
        display: inline-block;
        position: relative;
        top: 1px;
        font-size: $iconNormalSize;
        /* + span {
            margin-left: 7px;
        } */
    }

    &.no-slot {
        padding: 0 10px;
        height: 32px;
        min-width: 32px;
        .left-icon {
            margin-right: 0;
        }
        .right-icon {
            margin-left: 0;
        }
    }

    &.bk-default {
        &:hover,
        &.hover {
            border-color: $newBlackColor3;
            color: $newBlackColor2;
        }
        &:active,
        &.active {
            border-color: $newMainColor;
            color: $newMainColor;
        }
        &.is-disabled,
        &[disabled] {
            background-color: #faf9fd;
            border-color: $newGreyColor1;
            color: $newGreyColor;
            cursor: not-allowed;
        }
        &.is-loading {
            cursor: default;
            &:hover,
            &.hover {
                background-color: $defaultColor;
                border-color: $newGreyColor;
                opacity: 1;
            }
            &:active,
            &.active {
                background-color: $defaultColor;
                border-color: $newGreyColor;
            }
        }
        .bk-button-loading {
            div {
                background-color: $newBlackColor2;
            }
        }
    }

    &.bk-primary {
        @mixin create-button $newMainColor, $newMainColor1, $newMainColor4;
    }

    &.bk-success {
        @mixin create-button $newGreenColor, $newGreenColor1, $newGreenColor4;
    }

    &.bk-warning {
        @mixin create-button $newOrangeColor, $newOrangeColor1, $newOrangeColor4;
    }

    &.bk-danger {
        @mixin create-button $newRedColor, $newRedColor1, $newRedColor4;
    }

    &.bk-button-small {
        height: 26px;
        line-height: 24px;
        padding: 0 12px;
        font-size: $fnSmallSize;
        .bk-icon {
            font-size: $iconSmallSize;
        }
    }

    &.bk-button-large {
        height: 38px;
        line-height: 36px;
        padding: 0 20px;
        font-size: $fnLargeSize;
        .bk-icon {
            font-size: $iconLargeSize;
        }
    }

    &.is-outline {
        &.is-disabled,
        &[disabled] {
            background-color: $defaultColor;
            border-color: $newGreyColor1;
            color: $newGreyColor;
            cursor: not-allowed;
            &:hover,
            &.hover {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
            &.active,
            &:active {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
        }
    }
}

.bk-button-text {
    border: none;
    background: none;
    cursor: pointer;
    font-size: $fnNormalSize;
    color: $newBlackColor2;
    text-decoration: none;
    outline: none;
    height: 22px;
    padding: 0;

    .bk-loading-wrapper {
        display: inline-block;
    }

    .bk-button-loading {
        position: relative;
        display: inline-block;
        .bounce {
            width: 7px;
            height: 7px;
            border-radius: 100%;
            display: inline-block;
            background-color: $defaultColor;
            animation: button-loading 1.2s infinite ease-in-out both;
        }
        .bounce1 {
            animation-delay: -0.72s;
        }
        .bounce2 {
            animation-delay: -0.48s;
        }
        .bounce3 {
            animation-delay: -0.24s;
        }
        .bounce4 {
            animation-delay: 0s;
        }
        .bk-spin {
            position: relative;
            top: -1px;
        }
    }

    .bk-button-icon-loading {
        position: relative;
        top: 2px;
        .loading {
            width: 14px;
            height: 14px;
            border: 2px solid;
            border-right-color: transparent;
            border-radius: 50%;
            animation: button-icon-loading 1s linear infinite;
            margin: -7px auto 0;
            display: inline-block;
        }
    }

    .bk-icon {
        width: 14px;
        height: 16px;
        line-height: 16px;
        min-width: 14px;
        display: inline-block;
        position: relative;
        top: 1px;
        /* + span {
            margin-left: 7px;
        } */
        &.left-icon {
            margin-right: 2px;
        }
        &.right-icon {
            margin-left: 2px;
        }
    }

    &.no-slot {
        padding: 0 10px;
        height: 22px;
        .left-icon {
            margin-right: 0;
        }
        .right-icon {
            margin-left: 0;
        }
    }

    &.bk-default {
        &:hover,
        &.hover {
            border-color: $newBlackColor3;
            color: $newBlackColor2;
        }
        &:active,
        &.active {
            border-color: $newMainColor;
            color: $newMainColor;
        }
        &.is-disabled,
        &[disabled] {
            background-color: $defaultColor;
            border-color: $newGreyColor1;
            color: $newGreyColor;
            cursor: not-allowed;
        }
        &.is-loading {
            cursor: default;
            &:hover,
            &.hover {
                background-color: $defaultColor;
                border-color: $newGreyColor;
                opacity: 1;
            }
            &:active,
            &.active {
                background-color: $defaultColor;
                border-color: $newGreyColor;
            }
        }
        .bk-button-loading {
            div {
                background-color: $newBlackColor2;
            }
        }
    }

    &.bk-primary {
        color: $newMainColor;
        &:hover,
        &.hover {
            color: $newMainColor1;
        }
        &:active,
        &.active {
            color: $newMainColor4;
        }
    }

    &.bk-success {
        color: $newGreenColor;
        &:hover,
        &.hover {
            color: $newGreenColor1;
        }
        &:active,
        &.active {
            color: $newGreenColor4;
        }
    }

    &.bk-warning {
        color: $newOrangeColor;
        &:hover,
        &.hover {
            color: $newOrangeColor1;
        }
        &:active,
        &.active {
            color: $newOrangeColor4;
        }
    }

    &.bk-danger {
        color: $newRedColor;
        &:hover,
        &.hover {
            color: $newRedColor1;
        }
        &:active,
        &.active {
            color: $newRedColor4;
        }
    }

    /* is-disabled 样式要放在 bk-primary bk-success bk-warning bk-danger 后面 */
    &.is-disabled,
    &[disabled] {
        color: $newGreyColor;
        cursor: not-allowed;
        &:hover,
        &.hover {
            color: $newGreyColor;
        }
        &:active,
        &.active {
            color: $newGreyColor;
        }
    }

    &.bk-button-small {
        height: 26px;
        line-height: 24px;
        padding: 0 12px;
        font-size: $fnSmallSize;
    }

    &.bk-button-large {
        height: 42px;
        line-height: 40px;
        padding: 0 20px;
        font-size: $fnLargeSize;
    }

    &.is-outline {
        &.is-disabled,
        &[disabled] {
            background-color: $defaultColor;
            border-color: $newGreyColor1;
            color: $newGreyColor;
            cursor: not-allowed;
            &:hover,
            &.hover {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
            &.active,
            &:active {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
        }
    }
}

.bk-button-hover {
    height: 32px;
    line-height: 30px;
    display: inline-block;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    -webkit-appearance: none;
    padding: 0 15px;
    text-align: center;
    vertical-align: middle;
    font-size: $fnNormalSize;
    background-color: $defaultColor;
    border: 1px solid $newGreyColor;
    border-radius: 2px;
    box-sizing: border-box;
    color: $newBlackColor2;
    text-decoration: none;
    transition: background-color ease 0.3s;
    min-width: 68px;

    .bk-loading-wrapper {
        opacity: 0;
    }

    .bk-button-loading {
        width: 58px;
        display: inline-block;
        div {
            width: 6px;
            height: 6px;
            border-radius: 100%;
            display: inline-block;
            background-color: $newBlackColor2;
            animation: button-loading 1s infinite ease-in-out both;
        }
        .bounce1 {
            animation-delay: -0.72s;
        }
        .bounce2 {
            animation-delay: -0.48s;
        }
        .bounce3 {
            animation-delay: -0.24s;
        }
        .bounce4 {
            animation-delay: 0s;
        }
    }

    .bk-button-icon-loading {
        position: relative;
        top: 2px;
        .loading {
            width: 14px;
            height: 14px;
            border: 2px solid;
            border-right-color: transparent;
            border-radius: 50%;
            animation: button-icon-loading 1s linear infinite;
            margin: -7px auto 0;
            display: inline-block;
        }
    }

    .bk-icon {
        width: 14px;
        height: 16px;
        line-height: 16px;
        min-width: 14px;
        display: inline-block;
        position: relative;
        top: 1px;
        /* + span {
            margin-left: 7px;
        } */
        &.left-icon {
            margin-right: 2px;
        }
        &.right-icon {
            margin-left: 2px;
        }
    }

    &.no-slot {
        padding: 0 10px;
        height: 36px;
        min-width: 36px;
        .left-icon {
            margin-right: 0;
        }
        .right-icon {
            margin-left: 0;
        }
    }

    &.bk-primary {
        @mixin create-button-hover $newMainColor, $newMainColor4;
    }

    &.bk-success {
        @mixin create-button-hover $newGreenColor, $newGreenColor4;
    }

    &.bk-warning {
        @mixin create-button-hover $newOrangeColor, $newOrangeColor4;
    }

    &.bk-danger {
        @mixin create-button-hover $newRedColor, $newRedColor4;
    }

    &.bk-button-small {
        height: 26px;
        line-height: 24px;
        padding: 0 12px;
        font-size: $fnSmallSize;
    }

    &.bk-button-large {
        height: 42px;
        line-height: 40px;
        padding: 0 20px;
        font-size: $fnLargeSize;
    }

    &.is-outline {
        &.is-disabled,
        &[disabled] {
            background-color: $defaultColor;
            border-color: $newGreyColor1;
            color: $newGreyColor;
            cursor: not-allowed;
            &:hover,
            &.hover {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
            &.active,
            &:active {
                background-color: $defaultColor;
                border-color: $newGreyColor1;
                color: $newGreyColor;
            }
        }
    }
}

@-webkit-keyframes button-icon-loading {
    0% {
        transform:rotate(0)
    }
    100% {
        transform:rotate(360deg)
    }
}

@keyframes button-icon-loading {
    0% {
        transform:rotate(0)
    }
    100% {
        transform:rotate(360deg)
    }
}

.bk-button-group {
    font-size: 0;
    margin-left: 1px;
    display: inline-block;
    .bk-button {
        margin: 0 0 0 -1px;
        border-radius: 0;
        &.hover,
        &:hover {
            border-color: $newMainColor;
            color: $newMainColor;
            position: relative;
            z-index: 1;
        }
        &.is-disabled:hover {
            border-color: $newGreyColor1;
            color: $newGreyColor;
            position: relative;
            z-index: 0;
        }
        &:first-child {
            border-radius: 2px 0 0 2px;
        }
        &:last-child {
            border-radius: 0 2px 2px 0;
        }
        &.is-selected {
            background-color: $newMainColor3;
            border-color: $newMainColor;
            color: $newMainColor;
            position: relative;
            z-index: 1;
        }
        span  {
            .bk-icon {
                margin-left: 7px;
            }
        }
    }
}

.bk-text-button {
    border: none;
    background: none;
    color: $newMainColor;
    cursor: pointer;
    text-decoration: none;
    .bk-icon {
        width: 16px;
        min-width: 16px;
        margin-right: 5px;
        display: inline-block;
    }

    +.bk-text-button {
        margin-left: 15px;
    }
    &:hover {
        color: $primaryHoverColor;
        text-decoration: none;
    }
    &.is-disabled {
        color: #e6e6e6 !important;
        cursor: not-allowed;
    }
    &.bk-default {
        color: $fnMainColor;
        &:hover {
            color: darken($fnMainColor, 10%);
        }
    }
    &.bk-info {
        color: $newMainColor1;
        &:hover {
            color: $infoHoverColor;
        }
    }
    &.bk-primary {
        color: $newMainColor;
        &:hover {
            color: $primaryHoverColor;
        }
    }
    &.bk-success {
        color: $newGreenColor;
        &:hover {
            color: $successHoverColor;
        }
    }
    &.bk-warning {
        color: $newOrangeColor;
        &:hover {
            color: $warningHoverColor;
        }
    }
    &.bk-danger {
        color: $newRedColor;
        &:hover {
            color: $dangerHoverColor;
        }
    }
    .bk-icon {
        width: 16px;
        min-width: 16px;
    }
}

/*

.bk-text-button {
    border: none;
    background: none;
    color: $newMainColor;
    cursor: pointer;
    text-decoration: none;
    .bk-icon {
        width: 16px;
        min-width: 16px;
        margin-right: 5px;
        display: inline-block;
    }

    +.bk-text-button {
        margin-left: 15px;
    }
    &:hover {
        color: $primaryHoverColor;
        text-decoration: none;
    }
    &.is-disabled {
        color: #e6e6e6 !important;
        cursor: not-allowed;
    }
    &.bk-default {
        color: $fnMainColor;
        &:hover {
            color: darken($fnMainColor, 10%);
        }
    }
    &.bk-info {
        color: $newMainColor1;
        &:hover {
            color: $infoHoverColor;
        }
    }
    &.bk-primary {
        color: $newMainColor;
        &:hover {
            color: $primaryHoverColor;
        }
    }
    &.bk-success {
        color: $newGreenColor;
        &:hover {
            color: $successHoverColor;
        }
    }
    &.bk-warning {
        color: $newOrangeColor;
        &:hover {
            color: $warningHoverColor;
        }
    }
    &.bk-danger {
        color: $newRedColor;
        &:hover {
            color: $dangerHoverColor;
        }
    }
    .bk-icon {
        width: 16px;
        min-width: 16px;
    }
}

.bk-radio-group {
    display: inline-block;
    margin-left: 1px;
    .bk-radio-button {
        float: left;
        margin-left: -1px;
        min-width: 100px;
        height: 36px;
        line-height: 34px;
        white-space: nowrap;
        outline: none;
        cursor: pointer;
        padding: 0 15px;
        text-align: center;
        vertical-align: middle;
        font-size: $fnNormalSize;
        background-color: $defaultColor;
        border: 1px solid #d6d6d6;
        box-sizing: border-box;
        color: $fnMainColor;
        position: relative;
        &:nth-of-type(1) {
            border-top-left-radius: 2px;
            border-bottom-left-radius: 2px;
        }
        &:nth-last-of-type(1) {
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
        }
        &:hover {
            background-color: $defaultHoverColor;
            color: $fnMainColor;
            text-decoration: none;
            opacity: 1;
        }
        &.is-checked {
            color: #fff;
            background-color: $newMainColor;
            border-color: $newMainColor;
            z-index: 10;
        }
        &.is-disabled,
        &[disabled] {
            background-color: #fff !important;
            border-color: #e5e5e5 !important;
            color: #e6e6e6 !important;
            cursor: not-allowed;
        }
    }
    &.bk-radio-mini {

        .bk-radio-button {
            height: 24px;
            line-height: 22px;
            padding: 0 7px;
            min-width: 50px;
            font-size: $fnSmallSize;
        }
    }
    &.bk-radio-small {
        .bk-radio-button {
            height: 32px;
            line-height: 30px;
            padding: 0 10px;
            min-width: 80px;
        }
    }
    &.bk-radio-large {
        .bk-radio-button {
            height: 42px;
            line-height: 40px;
            padding: 0 16px;
            min-width: 120px;
        }
    }
}

 */

@-moz-keyframes button-loading {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1.0;
    }
}

@-webkit-keyframes button-loading {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1.0;
    }
}

@keyframes button-loading {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1.0;
    }
}
