/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
*/

/**
 * @file 默认英文语言包
 *
 * Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved. 蓝鲸智云 版权所有
 */

export default {
  bk: {
    lang: 'en-US',
    datePicker: {
      // test: 'we{vari}hello {ccc}!@#$%^&&*({})',
      // 选择日期
      selectDate: 'Select Date',
      selectTime: 'Select Time',
      clear: 'Clear',
      ok: 'OK',
      weekdays: {
        sun: 'Sun',
        mon: 'Mon',
        tue: 'Tue',
        wed: 'Wed',
        thu: 'Thu',
        fri: 'Fri',
        sat: 'Sat'
      },
      hour: 'Hour',
      min: 'Minute',
      sec: 'Second',
      toNow: 'Now',
      now: 'Now'
    },
    dialog: {
      ok: 'OK',
      cancel: 'CANCEL'
    },
    exception: {
      403: 'Forbidden',
      404: 'Not Found',
      500: 'Internal Server Error',
      building: 'Building',
      empty: 'No Data',
      searchEmpty: 'Search Is Empty',
      login: 'Please log in to Blueking'
    },
    form: {
      validPath: 'Please configure a valid path'
    },
    input: {
      input: 'Please input'
    },
    imageViewer: {
      loadFailed: 'Picture failed to load.',
      quitTips: 'ESC Can Exit fullscreen'
    },
    notify: {
      showMore: 'Show more'
    },
    select: {
      selectAll: 'Select All',
      pleaseselect: 'Please select',
      searchPlaceholder: 'Input keyword to search',
      dataEmpty: 'No options',
      searchEmpty: 'No matched data'
    },
    sideslider: {
      title: 'Title'
    },
    tagInput: {
      placeholder: 'Please input and press ENTER to finish'
    },
    transfer: {
      left: 'Left',
      total: ' (Total {total})',
      addAll: 'Add All',
      emptyContent: 'No Data',
      right: 'Right',
      removeAll: 'Remove All',
      emptySelected: 'No Selected',
      searchPlaceholder: 'Input keyword to search'
    },
    tree: {
      emptyText: 'No Data'
    },
    steps: {
      step1: 'Step1',
      step2: 'Step2',
      step3: 'Step3'
    },
    uploadFile: {
      drag: 'Try dragging an file here or',
      click: 'click to upload',
      uploadDone: 'Upload finished',
      uploading: 'uploading',
      reupload: 'reupload',
      replace: 'replace',
      uploadFailed: 'upload failed',
      fileExceedMsg: '{fileName} cannot exceed {size} MB',
      invalidFileName: 'FileName is not valid',
      invalidImageFormat: 'Only upload JPG | PNG | JPEG',
      imageExceedMsg: 'Image Size cannot exceed {imgSize} MB',
      uploadLabel: 'Upload Files'
    },
    navigation: {
      headerTitle: 'Program name'
    },
    searchSelect: {
      placeholder: 'Please enter',
      emptyText: 'Included key worth filtering query must have a value',
      condition: 'Or',
      remoteEmptyText: 'Query no data',
      remoteLoadingText: 'Loading...',
      tips: 'multiple keywords separate by |',
      ok: 'OK',
      cancel: 'Cancel'
    },
    table: {
      emptyText: 'No Data',
      sumText: 'Summary',
      setting: {
        title: 'Table Settings',
        fields: {
          title: 'Displaying Fields Setting',
          subtitle: '（{max} fiels most）',
          selectAll: 'All'
        },
        lineHeight: {
          title: 'Table Line Height',
          small: 'small',
          medium: 'medium',
          large: 'large'
        },
        options: {
          ok: 'OK',
          cancel: 'Cancel'
        }
      },
      confirm: 'Confirm',
      reset: 'Reset',
      all: 'All',
      filter: {
        placeholder: 'Please input keyword',
        empty: 'No matched data'
      }
    },
    bigTree: {
      emptyText: 'No Data'
    },
    message: {
      copy: 'copy',
      copied: 'copied',
      assistant: 'Assistant',
      details: 'Details',
      copySuccess: 'Successfully',
      copyFailed: 'Copy Failed'
    },
    image: {
      zoomIn: 'zoom in',
      zoomOut: 'zoom out',
      rotateLeft: 'anticlockwise',
      rotateRight: 'clockwise rotation',
      fullScreen: 'full screen',
      original: 'original size',
      prev: 'prev',
      next: 'next'
    },
    versionDetail: {
      currentTagText: 'Current'
    }
  }
}
