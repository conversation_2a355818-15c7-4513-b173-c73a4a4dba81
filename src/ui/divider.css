@import './variable.css';

.bk-divider {
  position: relative;

  &.bk-divider__vertical {
    display: inline-block;
    width: 0;
    height: 1em;
    margin: 0 8px;
    verticalAlign: middle;
  }

  &.bk-divider__horizontal {
    display: block;
    width: 100%;
    height: 0;
    margin: 1em 0;
    verticalAlign: middle;
  }

  .bk-divider-info {
    position: absolute;
    padding: 0 1.4em;
    color: $newBlackColor1;
    top:50%;
    transform: translateY(-50%);
    background-color: $defaultLightColor;
    &-left {
        left: 2em;
    }
    &-center {
        left: 50%;
        transform: translateX(-50%) translateY(-50%)
    }
    &-right {
        right: 2em;
    }
  }
}
