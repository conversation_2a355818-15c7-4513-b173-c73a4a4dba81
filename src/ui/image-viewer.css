@import './variable.css';

$imgWrapTextColor:$newGreyColor;
$tipsColor:$newBlackColor3;


.bk-image-viewer {
    &-wrapper {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, .6);
        display: flex;
        flex-direction: column;
    }
    &-mask{
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    &-header {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9;
        width: 100%;
        height: 52px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $imgWrapTextColor;
        background: rgba(0,0,0,0.70);
        > div {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 14px;
            padding: 0 25px;
        }

        .quit-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        .quit-tips{
            font-size: 14px;
            color: $tipsColor;
        }
    }

    &-btn {
        position: absolute;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        opacity: .8;
        box-sizing: border-box;
        background-color: rgba(0, 0, 0, .3);
        color: #DCDEE5;
        user-select: none;

        &:hover {
            background-color: rgba(0, 0, 0, .4);
            color: $defaultColor;
        }
    }

    &-close {
        height: 52px;
        line-height: 48px;
        text-align: center;
        font-size: 32px;
        color: #C4C6CC;
        cursor: pointer;
        &:hover{
            color: #fff;
        }
    }

    &-canvas {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center
    }

    &-has-header {
        padding-top: 52px;
    }

    &-error {
        text-align: center;
        font-size: 14px;
        color: $newBlackColor1;

        .bk-icon {
            font-size: 85px;
            line-height: 1;
            margin-bottom: 15px;
        }
    }

    &-actions {
        left: 50%;
        bottom: 30px;
        transform: translateX(-50%);
        width: 282px;
        height: 44px;
        padding: 0 23px;
        background-color: $newBlackColor4;
        border-color: $defaultLightColor;
        border-radius: 22px;
        &:hover{
            background: rgba(0,0,0,.5);
        }

        &-inner {
            width: 100%;
            height: 100%;
            text-align: justify;
            font-size: 23px;
            color: $imgWrapTextColor;
            display: flex;
            align-items: center;
            justify-content: space-around;

            .bk-icon {
                cursor: pointer;

                &:hover {
                    color: $defaultLightColor;
                }
            }

        }
    }

    &-next, &-prev {
        top: 50%;
        width: 80px;
        height: 80px;
        font-size: 40px;
        cursor: pointer;
    }

    &-prev {
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        left: 40px
    }

    &-next {
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 40px;
        text-indent: 2px
    }
}
