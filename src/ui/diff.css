@import "highlight.js/styles/googlecode.css";
@import "diff2html/dist/diff2html.css";

.bk-diff {
  .hljs {
    display: inline-block;
    padding: 0;
    background: transparent;
    vertical-align: middle;
    overflow-y: hidden;
  }
  .d2h-file-header {
    display: none;
  }
  .d2h-file-diff {
    &::-webkit-scrollbar {
      width: 6px;
      height: 5px;
      background-color: color(transparent lightness(80%));
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #e6e9ea;
    }
  }
  .d2h-files-diff {
    display: flex;
    .d2h-file-side-diff {
      flex: 1 1 50%;
      &:nth-child(n + 2) {
        .d2h-code-wrapper {
          border-left: 1px solid #fff;
        }
      }
      .d2h-code-side-linenumber {
        width: 60px;
        padding-right: 10px;
      }
    }
  }
  .d2h-info {
    background-color: transparent;
  }
  .d2h-diff-table {
    td {
      height: 20px;
      vertical-align: middle;
      overflow: hidden;
    }
  }
  .d2h-file-side-diff {
    &::-webkit-scrollbar {
      width: 6px;
      height: 5px;
      background-color: color(transparent lightness(80%));
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #e6e9ea;
    }
  }
  .hljs-comment,
  .hljs-quote {
    color: #c4c6cc;
    font-style: italic;
  }
  &.dark {
    .d2h-diff-table {
      background: #1d1d1d;
    }
    .d2h-file-side-diff {
      &:nth-child(n + 2) {
        .d2h-code-wrapper {
          border-left-color: #313238;
        }
      }
    }

    .d2h-code-linenumber,
    .d2h-code-side-linenumber {
      font-size: 12px;
      color: #8f8f89;
      border: none;
      background: #212121;
      &.d2h-change {
        background-color: #dfd;
      }
      &.d2h-emptyplaceholder {
        background: transparent;
      }
    }
    .d2h-info {
      color: inherit;
    }
    .d2h-del {
      background-color: #422321 !important;
      del {
        background: transparent;
      }
    }
    .d2h-ins {
      background-color: #2d3324 !important;
      ins {
        background: transparent;
      }
    }
    .d2h-emptyplaceholder {
      background-color: #292929 !important;
    }
    .hljs {
      color: #979ba5;
    }

    .hljs-keyword,
    .hljs-literal,
    .hljs-symbol,
    .hljs-name {
      color: #3a84ff;
    }
    .hljs-link {
      color: #569cd6;
      text-decoration: underline;
    }

    .hljs-built_in,
    .hljs-type {
      color: #4ec9b0;
    }

    .hljs-number,
    .hljs-class {
      color: #b8d7a3;
    }

    .hljs-string,
    .hljs-meta-string {
      color: #8f3aff;
    }

    .hljs-regexp,
    .hljs-template-tag {
      color: #9a5334;
    }

    .hljs-subst,
    .hljs-function,
    .hljs-title,
    .hljs-formula {
      color: #8f3aff;
    }
    .hljs-params {
      color: #ea3636;
    }

    .hljs-comment,
    .hljs-quote {
      color: #63656e;
      font-style: italic;
    }

    .hljs-doctag {
      color: #608b4e;
    }

    .hljs-meta,
    .hljs-meta-keyword,
    .hljs-tag {
      color: #9b9b9b;
    }

    .hljs-variable,
    .hljs-template-variable {
      color: #bd63c5;
    }

    .hljs-attr,
    .hljs-attribute,
    .hljs-builtin-name {
      color: #ff9c01;
    }

    .hljs-section {
      color: gold;
    }

    .hljs-emphasis {
      font-style: italic;
    }

    .hljs-strong {
      font-weight: bold;
    }

    /*.hljs-code {
        font-family:'Monospace';
        }*/

    .hljs-bullet,
    .hljs-selector-tag,
    .hljs-selector-id,
    .hljs-selector-class,
    .hljs-selector-attr,
    .hljs-selector-pseudo {
      color: #3a84ff;
    }

    .hljs-addition {
      background-color: #144212;
      display: inline-block;
      width: 100%;
    }

    .hljs-deletion {
      background-color: #600;
      display: inline-block;
      width: 100%;
    }
  }
  &.format-line-by-line {
    tbody.d2h-diff-tbody {
      tr:last-child,
      tr:nth-last-child(2) {
        display: none;
      }
    }
  }
  &.format-side-by-side {
    tbody.d2h-diff-tbody {
      tr:last-child {
        display: none;
      }
    }
  }
}
