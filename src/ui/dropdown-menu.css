@import './variable.css';

.bk-dropdown-menu {
  --font-size: 12px;
  &.medium-font {
    --font-size: 14px;
  }

  &.large-font {
    --font-size: 16px;
  }

  &.medium-font,
  &.large-font {
    .bk-dropdown-list {
      > li {
        > a {
          height: 36px;
          line-height: 37px;
        }
      }
    }
  }

  display: inline-block;
  position: relative;
  &.disabled, &.disabled * {
    cursor: not-allowed;
    color: $newGreyColor;
    border-color: $newGreyColor1 !important;
    background-color: #fafafa !important;
  }

  .bk-dropdown-trigger {
    font-size: var(--font-size);
    .bk-icon {
      transform-origin: center;
      transition: all ease 0.2s;
      &.icon-flip {
        transform: rotate(180deg);
      }
    }
    .bk-button {
      font-size: var(--font-size);
    }
  }

  .bk-dropdown-content {
    background: $defaultColor;
    padding: 4px 0;
    margin: 0;
    z-index: $dropdownMenuZIndex;
    border-radius: 2px;
    border: 1px solid $newGreyColor1;
    box-sizing: border-box;
    transition: visibility 0s, max-height 200ms linear;
    visibility: hidden;
    box-shadow: 0 2px 6px rgba(51, 60, 72, 0.1);
    text-align: left;
    display: inline-block;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 4px;
      /* background-color: lighten(#e6e9ea, 80%); */
      /* background-color: #e6e9ea; */
      background-color: transparent;
    }
    &::-webkit-scrollbar-thumb {
      height: 5px;
      border-radius: 2px;
      /* background-color: color(#e6e9ea lightness(80%)); */
      background-color: $newGreyColor1;
    }

    &.is-show {
      visibility: visible;
    }
  }
  .bk-dropdown-list {
    max-height: 166px;
    list-style: none;
    padding: 0;
    margin: 0;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 20px;
      background: $scrollbarColor;
      -webkit-box-shadow: inset 0 0 6px hsla(0,0%,80%,.3);
    }

    > li {
      width: 100%;
      margin: 0;
      display: inline-block;
      > a {
        display: block;
        height: 32px;
        line-height: 33px;
        padding: 0 16px;
        color: $newBlackColor2;
        font-size: var(--font-size);
        text-decoration: none;
        white-space: nowrap;
        &:hover {
          background-color: $newGreyColor2;
          color: $newMainColor;
        }
      }
      .bk-icon {
        margin-right: 5px;
      }
    }
  }
}
.bk-dropdown-full-width{
  .bk-dropdown-content {
    min-width: 100%;
  }
  .bk-dropdown-list {
    width: 100%;
  }
}
