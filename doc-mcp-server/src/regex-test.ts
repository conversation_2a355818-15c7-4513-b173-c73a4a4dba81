#!/usr/bin/env node

const testContent = `
### 属性 {page=#/button}
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|------|------|
| theme | 按钮类型 | String | 可以用按钮样式【\`default\` \`primary\` \`success\` \`warning\` \`danger\`】 | \`default\` |
| hover-theme | mouseover 按钮类型，当设置了此属性时，\`theme\` 和 \`text\` 失效 | String | 可以用按钮样式【\`primary\` \`success\` \`warning\` \`danger\`】 | —— |

### 事件 {page=#/button}
| 事件名称 | 说明 | 回调参数 |
|------|------|------|
| click | 点击事件  | 事件对象 event |
`;

console.log("Testing regex patterns...\n");

// 测试属性正则
console.log("1. Testing properties regex:");
const propRegex = /###\s*属性[^\n]*\n([\s\S]*?)(?=\n###|\n##|$)/;
const propMatch = testContent.match(propRegex);
if (propMatch) {
  console.log("✅ Properties match found!");
  console.log("Matched content:", propMatch[1]);
  
  const tableContent = propMatch[1];
  const rows = tableContent.split('\n').filter(line => line.includes('|') && line.trim() !== '');
  console.log(`Found ${rows.length} table rows:`);
  
  rows.forEach((row, index) => {
    console.log(`Row ${index}: ${row}`);
    if (index >= 2) { // Skip header and separator
      const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
      console.log(`  Cells (${cells.length}):`, cells);
    }
  });
} else {
  console.log("❌ No properties match");
}

console.log("\n2. Testing events regex:");
const eventRegex = /###\s*事件[^\n]*\n([\s\S]*?)(?=\n###|\n##|$)/;
const eventMatch = testContent.match(eventRegex);
if (eventMatch) {
  console.log("✅ Events match found!");
  console.log("Matched content:", eventMatch[1]);
} else {
  console.log("❌ No events match");
}
