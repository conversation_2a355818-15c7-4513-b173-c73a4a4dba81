{"name": "bk-magic-vue", "version": "2.5.10-beta.5", "description": "基于蓝鲸 Magicbox 和 Vue 的前端组件库", "main": "dist/bk-magic-vue.min.js", "files": ["lib", "dist", "bkui-vue2-helper"], "scripts": {"dll": "node build/build-dll.js", "dev": "node build/check-dll.js && cross-env NODE_ENV=dev node build/dev-server.js", "clean": "rimraf dist && rimraf lib && npm run clean:css", "clean:css": "rimraf lib/ui", "build": "npm run clean && npm run build:css && npm run build:component && npm run build:library && npm run build:version", "build:css": "gulp --gulpfile build/gulpfile.css.js build-css", "build:component": "gulp --gulpfile gulpfile.component.js build-component --max-old-space-size=4096", "build:library": "cross-env NODE_ENV=prod:all gulp --gulpfile gulpfile.library.js build-library", "build:source": "cross-env NODE_ENV=prod:source gulp --gulpfile gulpfile.library.js build-library", "build:min": "cross-env NODE_ENV=prod:min gulp --gulpfile gulpfile.library.js build-library", "build:example:analyzer": "cross-env NODE_ENV=example node build/build-example.js dev analyzer", "build:example": "cross-env NODE_ENV=example node build/build-example.js dev && npm run build:version", "build:version": "node build/build-version.js", "extract-chinese": "node tool/extract-chinese.js", "prepublishOnly": "npm run build", "extract": "node tool/extract-props.js", "test": "jest --config __test__/jest.config.js --no-cache"}, "keywords": ["bk-magic", "bk-magic-vue", "bk-magicbox", "bk-magicbox-vue", "magicbox", "<PERSON><PERSON>", "Vue.js", "component", "components", "ui", "framework"], "author": "bkfe", "license": "MIT", "devDependencies": {"@babel/cli": "~7.4.4", "@babel/core": "~7.4.5", "@babel/helper-module-imports": "~7.0.0", "@babel/plugin-external-helpers": "~7.2.0", "@babel/plugin-proposal-object-rest-spread": "~7.4.4", "@babel/plugin-syntax-dynamic-import": "~7.2.0", "@babel/plugin-syntax-jsx": "~7.2.0", "@babel/plugin-transform-async-to-generator": "~7.4.4", "@babel/plugin-transform-modules-commonjs": "~7.4.4", "@babel/plugin-transform-object-assign": "~7.2.0", "@babel/plugin-transform-runtime": "~7.4.4", "@babel/preset-env": "~7.4.5", "@babel/register": "~7.4.4", "@babel/runtime": "~7.4.5", "@babel/runtime-corejs2": "~7.4.5", "@blueking/magicbox-header": "~1.1.61", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@vue/babel-helper-vue-jsx-merge-props": "~1.0.0", "@vue/babel-plugin-transform-vue-jsx": "~1.0.0", "@vue/babel-preset-jsx": "~1.0.0", "@vue/component-compiler-utils": "~3.0.0", "@vue/test-utils": "~1.0.0-beta.29", "array-flat-polyfill": "~1.0.1", "babel-core": "~7.0.0-bridge.0", "babel-eslint": "~10.0.2", "babel-jest": "~24.9.0", "babel-loader": "~8.0.6", "babel-plugin-date-fns": "~0.2.1", "body-parser": "~1.19.0", "cheerio": "~1.0.0-rc.3", "compression-webpack-plugin": "~3.0.0", "connect-history-api-fallback": "~1.6.0", "copy-webpack-plugin": "~5.0.3", "core-js": "~2.6.9", "cpx": "~1.5.0", "cross-env": "~5.2.0", "css-loader": "~3.0.0", "cssnano": "~4.1.10", "date-fns": "~1.30.1", "diff": "~4.0.1", "diff2html": "~2.9.0", "eslint": "~7.20.0", "eslint-config-standard": "~12.0.0", "eslint-friendly-formatter": "~4.0.1", "eslint-loader": "~4.0.2", "eslint-plugin-import": "~2.22.1", "eslint-plugin-node": "~10.0.0", "eslint-plugin-promise": "~4.2.1", "eslint-plugin-standard": "~4.0.1", "eslint-plugin-vue": "~7.6.0", "eventsource-polyfill": "~0.9.6", "express": "~4.17.1", "file-loader": "~4.0.0", "friendly-errors-webpack-plugin": "~1.7.0", "fs-extra": "~8.0.1", "gulp": "~4.0.2", "gulp-base64": "~0.1.3", "gulp-gzip": "~1.4.2", "gulp-postcss": "~8.0.0", "gulp-rename": "~1.4.0", "gulp-replace": "~1.0.0", "gulp-rollup": "~2.16.2", "gulp-sourcemaps": "~2.6.5", "html-webpack-plugin": "~3.2.0", "http-proxy-middleware": "~0.19.1", "husky": "^4.3.0", "jest": "~24.9.0", "jest-canvas-mock": "^2.1.2", "jest-serializer-vue": "~2.0.2", "jest-transform-stub": "~2.0.0", "js-calendar": "~1.2.3", "markdown-it": "~12.3.2", "markdown-it-anchor": "~5.2.4", "markdown-it-attrs": "~2.4.1", "markdown-it-container": "~2.0.0", "markdown-it-replace-link": "~1.0.1", "markdown-it-table-of-contents": "~0.4.4", "mini-css-extract-plugin": "~0.7.0", "normalize-wheel": "~1.0.1", "npm": "~6.14.6", "optimize-css-assets-webpack-plugin": "~5.0.1", "ora": "~3.4.0", "parse5": "~5.1.0", "popper.js": "~1.15.0", "postcss": "~7.0.39", "postcss-atroot": "~0.1.3", "postcss-color-function": "~4.1.0", "postcss-conditionals": "^2.1.0", "postcss-discard-comments": "~4.0.2", "postcss-extend-rule": "~2.0.0", "postcss-for": "~2.1.1", "postcss-import": "~12.0.1", "postcss-loader": "~3.0.0", "postcss-mixins": "~6.2.1", "postcss-nested": "~4.1.2", "postcss-preset-env": "~6.6.0", "postcss-property-lookup": "~2.0.0", "postcss-simple-extend": "^1.0.0", "postcss-simple-vars": "~5.0.2", "postcss-url": "~8.0.0", "rimraf": "~2.6.3", "rollup": "~1.16.2", "rollup-plugin-alias": "~1.5.2", "rollup-plugin-babel": "~4.3.3", "rollup-plugin-cleanup": "~3.1.1", "rollup-plugin-commonjs": "~10.0.1", "rollup-plugin-copy": "~3.0.0", "rollup-plugin-gzip": "~2.2.0", "rollup-plugin-node-resolve": "~5.1.0", "rollup-plugin-postcss": "~2.0.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "~5.0.0", "rollup-plugin-vue": "~5.0.1", "rollup-pluginutils": "~2.8.1", "style-loader": "~0.23.1", "throttle-debounce": "~2.1.0", "transliteration": "~2.1.3", "url-loader": "~2.0.0", "vue": "2.7.16", "vue-i18n": "~8.11.2", "vue-jest": "~3.0.4", "vue-loader": "15.10.1", "vue-markdown-loader": "~2.5.0", "vue-router": "~3.0.6", "vue-style-loader": "~4.1.2", "vue-template-compiler": "2.7.16", "webpack": "~4.35.0", "webpack-bundle-analyzer": "~3.3.2", "webpack-dev-middleware": "~3.7.0", "webpack-hot-middleware": "~2.25.0", "webpack-merge": "~4.2.1", "webpack-parallel-uglify-plugin": "~1.1.0", "webpack-stream": "~5.2.1"}, "peerDependencies": {"vue": "^2.6.0"}, "dependencies": {"clipboard": "^2.0.11", "highlight.js": "^10.7.2", "json-formatter-js": "^2.3.4", "marked": "4.2.12", "spark-md5": "^3.0.2", "tinycolor2": "^1.4.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "vetur": {"tags": "./bkui-vue2-helper/tags.json", "attributes": "./bkui-vue2-helper/attributes.json"}, "engines": {"node": "<= 16.14"}}