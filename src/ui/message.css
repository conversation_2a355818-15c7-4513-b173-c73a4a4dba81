@import './variable.css';

.bk-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: fixed;
    left: 50%;
    padding: 10px 15px;
    background: $newMainColor5;
    border: 1px solid $newMainColor3;
    border-radius: 2px;
    box-shadow: 0px 2px 4px 0px rgba(16, 37, 74, 0.1);
    z-index: $messageZIndex;
    transform: translateX(-50%);
    transition: top .3s linear;
}
.bk-message-primary {
    background: $newMainColor5;
    border-color: $newMainColor3;
    .bk-message-icon .bk-icon {
        color: $newMainColor;
    }
    .bk-message-close .icon-close {
        color: $newMainColor2;
    }
}
.bk-message-error {
    background: $newRedColor5;
    border-color: $newRedColor3;
    box-shadow: 0px 2px 4px 0px rgba(86, 34, 34, 0.1);
    .bk-message-icon .bk-icon {
        color: $newRedColor;
    }
    .bk-message-close .icon-close {
        color: $newRedColor2;
    }
}
.bk-message-warning {
    background: $newOrangeColor5;
    border-color: $newOrangeColor3;
    box-shadow: 0px 2px 4px 0px rgba(75, 51, 13, 0.1);
    .bk-message-icon .bk-icon {
        color: $newOrangeColor;
    }
    .bk-message-close .icon-close {
        color: $newOrangeColor2;
    }
}
.bk-message-success {
    background: #f2fff4;
    border-color: $newGreenColor3;
    box-shadow: 0px 2px 4px 0px rgba(16, 68, 29, 0.1);
    .bk-message-icon .bk-icon {
        color: $newGreenColor;
    }
    .bk-message-close .icon-close {
        color: $newGreenColor2;
    }
}

.bk-message-icon {
    align-self: flex-start;
    flex-shrink: 0;
    margin-top: 1px;
    margin-right: 10px;
    color: #ffffff;
    .bk-icon {
        display: block;
        width: 18px;
        height: 18px;
        line-height: 18px;
        font-size: $iconSmallerSize;
        text-align: center;
        color: #ffffff;
        border-radius: 50%;
    }
}

.bk-message-content {
    box-flex: 1;
    flex-grow: 1;
    flex: 1;
    min-width: 230px;
    /* max-width: 538px; */
    line-height: 20px;
    font-size: 14px;
    text-align: left;
    color: $newBlackColor2;
    word-break: break-all;
    &.ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    &.multi-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: var(--line);
        -webkit-box-orient: vertical;
    }
}

.bk-message-close {
    align-self: flex-start;
    flex-shrink: 0;
    text-align: center;
    .bk-icon {
        display: block;
        margin-left: 30px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        color: $newBlackColor3;
        font-size: 18px;
        font-weight: bold;
        border-radius: 50%;
        cursor: pointer;
        &:hover {
            background-color: $newGreyColor2;
        }
    }
}

.bk-message-copy {
    font-size: 14px;
    align-self: flex-end;
    color: $newMainColor;
    cursor: pointer;
    margin-left: 5px;
    line-height: 20px;
}

.bk-message-copy.copied {
    color: $newBlackColor3;
    cursor: default;
}

.vertical-move-enter,
.vertical-move-leave-to {
    opacity: 0;
    margin-top: -50px;
}
.vertical-move-enter-active,
.vertical-move-leava-active {
    transition: margin .2s linear;
}

.bk-message-content {
    display: flex;
    width: 100%;
    word-break: break-all;

    &.multi {
      flex-direction: column;
      position: relative;

      .overview {
        display: flex;
        justify-content: space-between;
        align-items: self-start;

        .left-content {
          display: flex;

          .describe {
            display: -webkit-box;
            max-width: 560px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
          }
        }

        .tools {
          display: flex;
          align-items: center;

          .tool {
            display: flex;
            padding: 0 5px;
            font-size: 14px;
            cursor: pointer;
            align-items: center;

            .bk-message-close {
              padding-left: 0;


              &:hover {
                color: #979ba5;
                i {
                   color: #979ba5;
                }
              }

              i {
                margin-left: 0;
                color: #c4c6cc;
              }
            }

            .message-action-text {
              padding-left: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &:not(.fix) {
              span {
                color: #3a84ff;
              }
            }

            &.fix {
              &.fixed {
                span {
                  color: #3a84ff;
                }
              }

              span {
                color: #979ba5;
              }

              &:hover {
                span {
                  color: #3a84ff;
                }
              }
            }

            &.close {
              span {
                color: #c4c6cc;

                &:hover {
                  color: #979ba5;
                }
              }
            }
          }
        }
      }

      .message-detail {
        position: relative;
        margin-top: 10px;

        .message-tree {
          max-height: 480px;
          min-height: 300px;
          padding: 14px 24px;
          overflow: auto;
          background: #fff;
          border-radius: 2px;

          .message-row {
            display: inline-flex;
            font-size: 14px;
            line-height: 34px;
            color: #313238;

            label {
              display: inline-block;
              width: 94px;
              min-width: 94px;
              margin-right: 8px;
              overflow: hidden;
              color: #63656e;
              text-align: right;
              text-overflow: ellipsis;
              white-space: nowrap;

              &::after {
                content: ':';
              }
            }

            .copy-value {
              padding-left: 4px;

              &:hover {
                background: rgba(186, 188, 192, 0.2);
                cursor: pointer;
                border-radius: 2px;
              }
            }
          }
        }

        .message-copy {
          position: absolute;
          top: 0px;
          right: 0px;
          font-size: 18px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          border-radius: 0 2px 0 6px;
          cursor: pointer;
          justify-content: center;
          background: #F5F7FA;
          color: #9FA3AE;

          &:hover {
            background: #F0F5FF;
            span {
              color: #3a84ff;

            }
          }
        }

        .copy-status {
          display: none;
          padding-bottom: 4px;

          .inner {
            position: relative;
            display: flex;
            padding: 6px 8px;
            font-size: 12px;
            line-height: 20px;
            color: #63656e;
            background: #fff;
            border: 1px solid #dcdee5;
            box-shadow: 0 2px 6px 0 #0000001a;
            align-items: center;

            span {
              margin-right: 4px;
            }

            &::after {
              position: absolute;
              bottom: 0;
              left: 50%;
              width: 8px;
              height: 8px;
              background: #fff;
              border-color: #dcdee5;
              border-style: solid;
              border-width: 0px 1px 1px 0px;
              content: '';
              transform: translate(0, 4px) rotate(45deg);
              box-shadow: 5px 3px 6px 0 #0000001a;
              box-sizing: border-box;
            }
          }

          &.success {
            .inner {
              span {
                color: #2dcb9d;
              }
            }
          }

          &.error {
            .inner {
              span {
                color: #ea3636;
              }
            }
          }

          &.is-show {
            position: absolute;
            top: 0;
            left: 0;
            display: inline-flex;
            white-space: nowrap;
          }

          &.is-hidden {
            top: -9999;
            left: -9999;
          }
        }
      }
    }
  }
