#!/usr/bin/env node

import { MCPClient } from "@mastra/mcp";
import path from "path";

async function testClient() {
  console.log("🧪 Testing BKUI Documentation MCP Client...\n");

  // 创建 MCP 客户端，连接到我们的服务器
  const mcp = new MCPClient({
    servers: {
      bkuiDocs: {
        command: "npx",
        args: ["tsx", "src/stdio.ts"],
        cwd: process.cwd(),
        env: {
          BKUI_COMPONENTS_PATH: path.resolve(process.cwd(), '../example/components')
        }
      }
    }
  });

  try {
    console.log("📡 Connecting to BKUI Documentation MCP Server...");
    
    // 获取工具
    const tools = await mcp.getTools();
    console.log(`✅ Connected! Available tools: ${Object.keys(tools).join(', ')}\n`);

    // 测试 component-list 工具
    console.log("1. 🔧 Testing component-list tool...");
    const componentListTool = tools['bkuiDocs_componentList'];
    if (componentListTool) {
      try {
        const result = await componentListTool.execute({
          context: { includeDescription: true },
          runtimeContext: {} as any
        });
        console.log(`   ✅ Found ${result.total} components`);
        console.log(`   📝 First 3 components:`);
        result.components.slice(0, 3).forEach((comp: any) => {
          console.log(`      - ${comp.name} (${comp.displayName}): ${comp.description.substring(0, 50)}...`);
        });
      } catch (error) {
        console.log(`   ❌ Error: ${error}`);
      }
    }
    console.log();

    // 测试 component-search 工具
    console.log("2. 🔍 Testing component-search tool...");
    const componentSearchTool = tools['bkuiDocs_componentSearch'];
    if (componentSearchTool) {
      try {
        const result = await componentSearchTool.execute({
          context: { query: "button", limit: 3 },
          runtimeContext: {} as any
        });
        console.log(`   ✅ Found ${result.total} components matching "button"`);
        result.results.forEach((comp: any) => {
          console.log(`      - ${comp.name}: ${comp.description.substring(0, 50)}...`);
        });
      } catch (error) {
        console.log(`   ❌ Error: ${error}`);
      }
    }
    console.log();

    // 测试 component-guide 工具
    console.log("3. 📖 Testing component-guide tool...");
    const componentGuideTool = tools['bkuiDocs_componentGuide'];
    if (componentGuideTool) {
      try {
        const result = await componentGuideTool.execute({
          context: { 
            componentName: "button", 
            sections: ["properties", "events"] 
          },
          runtimeContext: {} as any
        });
        console.log(`   ✅ Retrieved guide for ${result.component.displayName}`);
        console.log(`   📋 Properties: ${result.properties?.length || 0}`);
        console.log(`   🎯 Events: ${result.events?.length || 0}`);
        
        if (result.properties && result.properties.length > 0) {
          console.log(`   📝 First property: ${result.properties[0].name} (${result.properties[0].type})`);
        }
      } catch (error) {
        console.log(`   ❌ Error: ${error}`);
      }
    }

    console.log("\n✅ All tests completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // 断开连接
    await mcp.disconnect();
    console.log("👋 Disconnected from server");
  }
}

// 运行测试
testClient().catch(console.error);
