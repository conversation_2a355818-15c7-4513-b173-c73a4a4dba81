<!--
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 *
 * Terms of the MIT License:
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
-->

<template>
  <li class="bk-option"
    :class="{
      'is-disabled': disabled
    }"
    @click="handleOptionClick">
    <div class="bk-option-content">
      <span class="bk-option-name">
        {{t('bk.select.selectAll')}}
        <template v-if="isAllSelected">
          {{`(${select.selectedOptions.length})`}}
        </template>
      </span>
    </div>
  </li>
</template>

<script>
import locale from 'bk-magic-vue/lib/locale'

export default {
  name: 'bk-option-all',
  mixins: [locale.mixin],
  inject: ['select'],
  data () {
    return {
      enabledOptions: []
    }
  },
  computed: {
    disabled () {
      return !this.enabledOptions.length
    },
    isAllSelected () {
      // enableOptions 会根据 options 变化而重置，会使用 filter 方法过滤 disabled 的 option
      // 因此可以把是否全选的依据设置为 selected 的长度是否等于 enableOptions 的长度
      return this.select.selected.length === this.enabledOptions.length
    }
  },
  watch: {
    'select.options': {
      immediate: true,
      handler (options) {
        this.setEnabledOptions()
      }
    }
  },
  methods: {
    setEnabledOptions () {
      this.enabledOptions = this.select.options.filter(option => !option.disabled)
    },
    handleOptionClick () {
      if (this.disabled) {
        return false
      }
      if (this.isAllSelected) {
        this.select.reset()
      } else {
        this.select.selectAll()
      }
    }
  }
}
</script>
