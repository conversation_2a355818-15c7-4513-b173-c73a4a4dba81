/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
*/

/**
 * @file message instance
 *
 * Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved. 蓝鲸智云 版权所有
 */

import Vue from 'vue'
import Message from './message.vue'

class MessageQueueMaker {
  constructor () {
    this.timer = null
    this.store = []
    this.maskMap = new WeakMap()
  }

  appendMaker (fn) {
    this.store.push(fn)
    this.executeMaker()
  }

  executeMaker () {
    this.timer && cancelAnimationFrame(this.timer)
    this.timer = requestAnimationFrame(() => {
      const fn = this.store.pop()
      if (typeof fn === 'function') {
        Reflect.apply(fn, this, [])
        this.executeMaker()
      }
    })
  }
}

const messageQueueMaker = new MessageQueueMaker()

const MessageComponent = Vue.extend(Message)
const getAdvanceMessageContainer = (parent = document.body) => {
  const zIndexArr = messageList.map((message) => message.zIndex) || []
  const maxZIndex = Math.max.apply(null, zIndexArr) || 2500

  let target = parent.querySelector('[data-msg-advanced-conmtainer]')
  if (!target) {
    target = document.createElement('div')
    target.setAttribute('data-msg-advanced-conmtainer', 'true')
    target.style.setProperty('position', 'fixed')
    parent.append(target)
  }

  target.style.zIndex = maxZIndex
  target.style.setProperty('display', 'block')
  return target
}

const messageList = []
let seed = 0

const BkMessage = function (config) {
  const formatConfig = () => {
    if (typeof config === 'object') {
      if (config.message && !config.message.hasOwnProperty('componentOptions')) {
        return JSON.parse(JSON.stringify(config))
      }
    }

    return config
  }
  const originConfig = formatConfig()
  // 限制个数为 0 时，清除所有实例
  if (config.limit === 0) {
    BkMessage.batchClose()
    return
  }

  if (config.limit > 0) {
    BkMessage.batchClose(config.limit)
  }

  const instanceId = `messageInstance_${Date.now()}_${seed++}`
  const offsetY = parseInt(config.offsetY) || 30 // 组件距视口顶部的偏移量
  const spacing = parseInt(config.spacing) || 10 // 组件间的垂直间距

  if (config.ellipsisLine === null || config.ellipsisLine === undefined || config.ellipsisLine === ''
    || isNaN(config.ellipsisLine)
  ) {
    config.ellipsisLine = 1
  }
  const ellipsisLine = config.ellipsisLine

  let verticalOffset = offsetY

  if (typeof config === 'string' || typeof config === 'number') {
    config = {
      message: config
    }
  }
  const configClose = config.onClose
  config.onClose = function () {
    BkMessage.close(instanceId, configClose)
  }

  const instance = new MessageComponent({
    data: config
  })

  if (originConfig.message !== null
        && typeof originConfig.message === 'object'
        && originConfig.message.hasOwnProperty('componentOptions')
  ) {
    instance.$slots.default = [config.message]
    instance.message = null
  }

  if (!originConfig.width) {
    instance.width = 560
  }

  if (originConfig.message !== null
    && typeof originConfig.message === 'object'
    && !originConfig.message.hasOwnProperty('componentOptions')) {
    instance.showAdvanced = true
    instance.actions = originConfig.actions || []

    if (!originConfig.width) {
      instance.width = 800
    }

    if (!originConfig.delay) {
      instance.delay = 8000
    }
  }

  instance.id = instanceId
  instance.spacing = spacing
  instance.ellipsisLine = ellipsisLine
  instance.verticalOffset = 0
  instance.$mount()
  instance.dom = instance.$el

  const mountedElementToTarget = () => {
    messageList.forEach(item => {
      verticalOffset += parseInt(item.$el.offsetHeight) + parseInt(spacing)
    })
    instance.verticalOffset = verticalOffset
    instance.horizonOffset = spacing
    instance.visible = true
    messageList.push(instance)

    const target = instance.showAdvanced ? getAdvanceMessageContainer(document.body) : document.body
    target.appendChild(instance.$el)

    if (instance.showAdvanced) {
      if (instance.$el) {
        target.style.setProperty('top', `${offsetY}px`)

        target.style.setProperty('left', '50%')
        target.style.setProperty('transform', 'translateX(-50%)')
        instance.$el.style.setProperty('position', 'relative')
        instance.$el.style.setProperty('margin-bottom', `${spacing}px`)
        instance.verticalOffset = 0
        instance.$on('message-show', isShow => {
          if (isShow) {
            messageList.forEach((item, i) => {
              if (item.id !== instanceId && item.showAdvanced) {
                if (item.$refs.refMessageAdvanced && item.$refs.refMessageAdvanced.isDetailShow) {
                  item.$refs.refMessageAdvanced.setDetailsShow(null, false)
                }
              }
            })
          }
        })
      }
    }
  }

  if (instance.showAdvanced) {
    messageQueueMaker.appendMaker(mountedElementToTarget)
  } else {
    mountedElementToTarget()
  }

  return instance
}
// 实例关闭回调
BkMessage.close = function (id, configClose) {
  let instanceIndex = -1
  messageList.some((item, index) => {
    if (item.id === id) {
      instanceIndex = index
      return true
    }
  })

  if (instanceIndex > -1) {
    const instance = messageList[instanceIndex]

    if (typeof configClose === 'function') {
      configClose(instance)
    }

    messageList.forEach((item, i) => {
      if (i > instanceIndex) {
        const targetVal = item.verticalOffset - (instance.dom.offsetHeight + instance.spacing)
        item.verticalOffset = targetVal > 0 ? targetVal : 0
      }
    })

    messageList.splice(instanceIndex, 1)

    if (!messageList.some(msg => msg.showAdvanced)) {
      const target = getAdvanceMessageContainer(document.body)
      target.style.setProperty('display', 'none')
    }
  }
}
// 批量清除实例
BkMessage.batchClose = function (limit = 0) {
  const len = messageList.length

  if (limit <= len) {
    const InstancesShouldClose = messageList.slice(0, len - limit + 1)
    InstancesShouldClose.forEach(item => {
      item.close()
    })
  }
}

Vue.prototype.$bkMessage = BkMessage

export default BkMessage
