@import './variable.css';
@import './mixins/ellipsis';

/* usage: @mixin get-theme red; */
@define-mixin get-theme $theme {
  .bk-step {
    &.done {
      .bk-step-number,
      .bk-step-icon {
        border-color: $theme;
        color: $theme;
      }

      /* horizontal */
      .bk-step-title,
      .bk-step-indicator {
        &::after {
          background-image: linear-gradient(to right, $theme 50%, transparent 0%);
        }
      }

      /* vertical */
      &::after {
        background-image: linear-gradient($theme 50%, transparent 0%);
      }

      &:has(+ .current.bk-step-error) {
        .bk-step-title,
        .bk-step-indicator {
          &::after {
            background-image: linear-gradient(to right, $newRedColor 50%, transparent 0%);
          }
        }
      }
      &:has(+ .current.bk-step-error) {
        &::after {
          background-image: linear-gradient($newRedColor 50%, transparent 0%);
        }
      }
    }
    &.current {
      .bk-step-number,
      .bk-step-icon,
      .bk-step-text {
        background-color: $theme;
        border-color: $theme;
        color: #fff;
      }
    }
  }
}

@define-mixin after-line {
  &::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 12px;
    width: 99999px;
    height: 1px;
    background-color: transparent;
  }
}

@keyframes loadingCircle {
  100% {
    transform: rotate(360deg);
  }
}

.bk-steps {
  display: table;
  width: 100%;
  display: flex;
  font-size: 0;
  .bk-icon {
    font-size: $iconLargeSize;
    line-height: 24px;

    &.icon-loading {
      display: inline-block;
      animation: loadingCircle 1s infinite linear;
      font-size: $iconSmallestSize;
    }
  }
  @mixin get-theme $newMainColor;
  .bk-step {
    flex: 1;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    margin-right: 16px;
    .bk-step-indicator {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 0;
      margin-right: 8px;
      border: 1px solid #979ba5;
      border-radius: 50%;
      line-height: 24px;
      color: #979ba5;
      text-align: center;
      background-color: #fff;
      z-index: 1;
      vertical-align: top;
    }
    .bk-step-number {
      font-size: 14px;
      font-family: arial;
    }
    .bk-step-content {
      display: inline-block;
      vertical-align: top;
    }
    .bk-step-title {
      display: inline-block;
      position: relative;
      color: #63656e;
      font-size: 14px;
      word-break: break-all;
      padding-right: 16px;
      line-height: 24px;
      @mixin after-line;
    }
    .bk-step-description {
      font-size: 12px;
      color: #979ba5;
      max-width: 140px;
      white-space: normal;
      @mixin ellipsis 140px, block;
    }
    &.bk-step-no-content {
      .bk-step-indicator {
        margin-right: 0;
        @mixin after-line;
        &::after {
          left: 32px;
          top: 10px;
        }
      }
    }
    &:last-child {
      flex: none;
      margin-right: 0;
      .bk-step-title {
        padding-right: 0;
        &::after {
          display: none;
        }
      }
    }
    &.done {
      .bk-step-title {
        color: $fnMainColor;
      }
      .bk-step-description {
        color: #979ba5;
      }
    }
    &.current {
      .bk-step-number,
      .bk-step-icon,
      .bk-step-text {
        border-width: 2px;
      }
      .bk-step-title,
      .bk-step-description {
        color: $fnMainColor;
      }

      &.bk-step-error {
        .bk-step-indicator {
          background-color: $newRedColor1;
          border-color: $newRedColor1;
        }
        .bk-step-title,
        .bk-step-description {
          color: $newRedColor1;
        }
      }
    }
  }
  &-info {
    @mixin get-theme $infoColor;
  }
  &-success {
    @mixin get-theme $newGreenColor;
  }
  &-warning {
    @mixin get-theme $newOrangeColor;
  }
  &-danger {
    @mixin get-theme $newRedColor;
  }

  &-dashed {
    .bk-step {
      .bk-step-title,
      .bk-step-indicator {
        &::after {
          background-image: linear-gradient(to right, $newGreyColor 50%, transparent 0%);
          background-position: bottom;
          background-size: 6px 1px;
          background-repeat: repeat-x;
        }
      }
    }
  }

  &-solid {
    .bk-step {
      .bk-step-title,
      .bk-step-indicator {
        &::after {
          background-color: $newGreyColor;
        }
      }
    }
  }

  &-vertical {
    flex-direction: column;
    height: 100%;
    .bk-step {
      margin-bottom: 16px;
      margin-right: 0;
      .bk-step-title,
      .bk-step-indicator {
        &::after {
          display: none;
        }
      }
      &::after {
        content: '';
        position: absolute;
        left: 12px;
        top: 40px;
        height: 100%;
        width: 1px;
        background-image: linear-gradient($newGreyColor 50%, transparent 0%);
        background-position: right;
        background-size: 1px 6px;
        background-repeat: repeat-y;
      }
      .bk-step-title {
        display: flex;
        height: 100%;
        align-items: center;
        text-align: left;
        margin-top: 0;
      }
    }
  }

  &-small {
    .bk-step {
      .bk-step-indicator {
        width: 20px;
        height: 20px;
        line-height: 20px;
      }

      .bk-step-title {
        font-size: 12px;
        line-height: 20px;
      }

      .bk-step-number {
        font-size: 12px;
      }

      .bk-icon {
        font-size: 12px;
        line-height: 20px;
      }

      &::after {
        left: 10px;
        top: 28px;
      }

      .bk-step-title {
        &::after {
          top: 10px;
        }
      }

      &.bk-step-no-content {
        .bk-step-indicator {
          &::after {
            left: 28px;
            top: 8px;
          }
        }
      }
    }
  }
}
