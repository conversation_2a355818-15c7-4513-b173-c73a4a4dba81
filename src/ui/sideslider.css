@import "./variable.css";

.bk-sideslider-show {
    overflow-y: hidden !important;
    body {
        overflow-y: hidden !important;
    }
    &.has-sideslider-padding {
        padding-right: 17px !important;
    }
}
.bk-sideslider {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    /* background-color: rgba(0, 0, 0, .6); */
    z-index: $sideSliderZIndex;
    &-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        background-color: #fff;
        overflow-y: auto;
        pointer-events: all;
        &.left {
            left: 0;
            box-shadow: 5px 0px 8px -5px rgba(0, 0, 0, 0.2);
        }
        &.right {
            right: 0;
            box-shadow: -5px 0px 8px -5px rgba(0, 0, 0, 0.2);
        }
    }
    &-header {
        width: 100%;
        height: 52px;
        background: #FFFFFF;
        &:before,
        &:after {
            content: '';
            display: table;
            line-height: 0;
        }
        &:after {
            clear: both;
        }
    }
    &-closer {
        width: 30px;
        height: 52px;
        line-height: 52px;
        background-color: #3a84ff;
        text-align: center;
        color: #fff;
        cursor: pointer;
        font-size: $iconLargeSize;
        span {
            display: block;
            margin-top: 22px;
        }
    }
    &-title {
        height: 52px;
        line-height: 52px;
        border-bottom: 1px solid #DCDEE5;
        font-size: 16px;
        /* font-weight: bold; */
        color: $newBlackColor1;
        .title-content {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
    }
    &-content {
        overflow: auto;
    }
    &-footer {
        width: 100%;
        height: 48px;
        /* border-top: 1px solid transparent; */
        border-top: 1px solid #dcdee5;
        background-color: #fff;
        display: flex;
        align-items: center;
    }
}
