[[toc]]

## Color 色彩

`bk-magic-vue` 使用以下调色板的颜色作为开发与设计规范，为构建的产品提供一致的外观视觉感受。颜色主要分为主色、辅助色、中性色三部分，主色部分为蓝鲸系列产品使用的主题色；而辅助色主要适用于状态类标识，也会用到个别需要特殊处理的元素；中性色主要使用在文字、边框、线条、背景等等。

### 主色 {page=#/color}
产品主色，蓝色代表严谨、理性和科技。

<div class="color-wrapper">
    <div class="color-item">
        <div class="main-color" style="background-color: #3a84ff;color: #aecdff;"><span>#3A84FF</span></div>
        <div class="other-color">
            <div style="background-color: #1768ef; color: #f5f7fa;"><span>#1768EF</span></div>
            <div style="background-color: #699df4; color: #f5f7fa;"><span>#699DF4</span></div>
            <div style="background-color: #a3c5fd; color: #63656e;"><span>#A3C5FD</span></div>
            <div style="background-color: #e1ecff; color: #979ba5;"><span>#E1ECFF</span></div>
        </div>
        <div class="color-tips">强调、突出、链接等</div>
    </div>
</div>

### 辅助色 {page=#/color}
运维行业主要是对线上业务进行维护和检测，更为注重业务健康状况，为此使用红（危险）、橙（预警）、绿（健康）三种颜色作为辅助色。

<div class="color-wrapper help-color">
    <div class="color-item">
        <div class="main-color" style="background-color: #ea3636;color: #ffa7ab;"><span>#EA3636</span></div>
        <div class="other-color">
            <div style="background-color: #ff5656;"><span>#FF5656</span></div>
            <div style="background-color: #fd9c9c;"><span>#FD9C9C</span></div>
            <div style="background-color: #ffdddd; color: #979ba5;"><span>#FFDDDD</span></div>
        </div>
        <div class="color-tips">危险、致命</div>
    </div>
    <div class="color-item">
        <div class="main-color" style="background-color: #ff9c01;color: #ffcca2;"><span>#FF9C01</span></div>
        <div class="other-color">
            <div style="background-color: #ffb848;"><span>#FFB848</span></div>
            <div style="background-color: #ffd695;"><span>#FFD695</span></div>
            <div style="background-color: #ffe8c3; color: #979ba5;"><span>#FFE8C3</span></div>
        </div>
        <div class="color-tips">预警、提醒</div>
    </div>
    <div class="color-item">
        <div class="main-color" style="background-color: #2dcb56;color: #7ee5c2;"><span>#2DCB56</span></div>
        <div class="other-color">
            <div style="background-color: #45e35f;"><span>#45E35F</span></div>
            <div style="background-color: #94f5a4;"><span>#94F5A4</span></div>
            <div style="background-color: #dcffe2; color: #979ba5;"><span>#DCFFE2</span></div>
        </div>
        <div class="color-tips">健康、正常、完成</div>
    </div>
</div>

### 中性色 {page=#/color}
在中性色的设计上加入环境色，使颜色更自然，和主题色搭配更为和谐。

<div class="color-wrapper middle-color">
    <div class="color-item color-item-max">
        <div class="main-color" style="background-color: #63656e;color: #979ba5;"><span>#63656E</span></div>
        <div class="other-color">
            <div style="background-color: #000000;"><span>#000000</span></div>
            <div style="background-color: #313238;"><span>#313238</span></div>
            <div style="background-color: #979ba5;"><span>#979BA5</span></div>
            <div style="background-color: #c4c6cc;"><span>#C4C6CC</span></div>
        </div>
        <div class="color-tips">字体、图标，禁用</div>
    </div>
    <div class="color-item color-item-max">
        <div class="main-color" style="background-color: #dcdee5; color: #979ba5;"><span>#DCDEE5</span></div>
        <div class="other-color">
            <div style="background-color: #eaebf0; color: #63656e;"><span>#EAEBF0</span></div>
            <div style="background-color: #f0f1f5; color: #63656e;"><span>#F0F1F5</span></div>
            <div style="background-color: #f5f7fa; color: #63656e;"><span>#F5F7FA</span></div>
            <div style="background-color: #fafbfd; color: #63656e;"><span>#FAFBFD</span></div>
            <div style="background-color: #ffffff; color: #63656e;"><span>#FFFFFF</span></div>
        </div>
        <div class="color-tips">边框、底色、背景</div>
    </div>
</div>

### 使用规范 {page=#/color}

<div class="color-spec">
    <div>1. 重点表示、强调、链接以及带有明确指示性</div>
    <div>2. 警示灯引起注意类标识</div>
    <div>3. 次级标题、内容文字，主要文字使用颜色</div>
</div>
