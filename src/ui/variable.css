/* 存放常用变量 */

/********************** 字体 **********************/
$newFontFamilyMac: -apple-system, BlinkMacSystemFont, PingFang SC, Microsoft Yahei, Helvetica, Aria;
$newFontFamilyWin: Microsoft Yahei, PingFang SC, Helvetica, Aria;

/********************** 主题色 **********************/
/* 重点表示、强调、链接以及带有明确指示性 */
$newMainColor: #3a84ff;
$newMainColor1: #699df4;
/* 描边使用 */
$newMainColor2: #a3c5fd;
/* 背景色 */
$newMainColor3: #e1ecff;
$newMainColor4: #2761dd;
$newMainColor5: #f0f8ff;
$newMainColor6: #e1e8f4;

/********************** 辅助色 **********************/
/* 退出、删除、错误、严重警告等否定类标识 */
$newRedColor: #ea3636;
$newRedColor1: #ff5656;
/* 描边使用 */
$newRedColor2: #fd9c9c;
/* 背景色 */
$newRedColor3: #ffdddd;
$newRedColor4: #db2626;
$newRedColor5: #ffeded;
$newRedColor6: #f6dada;

/* 警示灯引起注意类标识 */
$newOrangeColor: #ff9c01;
$newOrangeColor1: #ffb848;
/* 描边使用 */
$newOrangeColor2: #ffd695;
/* 背景色 */
$newOrangeColor3: #ffe8c3;
$newOrangeColor4: #eb9000;
$newOrangeColor5: #fff4e2;
$newOrangeColor6: #ede6db;

/* 成功、完成、进行等肯定类标识 */
$newGreenColor: #2dcb56;
$newGreenColor1: #45e35f;
/* 描边使用 */
$newGreenColor2: #94f5a4;
/* 背景色 */
$newGreenColor3: #dcffe2;
$newGreenColor4: #1ab943;
$newGreenColor5: #eaffed;
$newGreenColor6: #cef0d7;

/********************** 中性色 **********************/
/* 重要级文字信息、内容标题信息 */
$newBlackColor: #000;
/* 次级标题、内容文字 主要文字使用颜色 */
$newBlackColor1: #313238;
/* 三级文字标题、内容文字 */
$newBlackColor2: #63656e;
/* 边框内说明文字 */
$newBlackColor3: #979ba5;
$newBlackColor4: rgba(0,0,0,0.4);

/* 按钮、表单边框颜色、禁用时文本颜色 */
$newGreyColor: #c4c6cc;
/* 表格线颜色区别表单，大描边颜色、线性色 */
$newGreyColor1: #dcdee5;
/* 表格头部背景色、禁用底色 */
$newGreyColor2: #f0f1f5;
/* 大面积背景色 */
$newGreyColor3: #fafbfd;

/********************** 基础颜色 **********************/
/* 默认 */
$defaultColor: #fff;
/* 主要 */
$primaryColor: #3c96ff;
/* 信息 */
$infoColor: #88c3ff;
/* 成功 */
$successColor: #2dcb56;
/* 警告 */
$warningColor: #ffb848;
/* 危险 */
$dangerColor: #ff5656;

/********************** 基础颜色-鼠标移上 **********************/
/* 默认 */
$defaultHoverColor: #fafafa;
/* 主要 */
$primaryHoverColor: #0082ff;
/* 信息 */
$infoHoverColor: #7ab9fa;
/* 成功 */
$successHoverColor: #00c873;
/* 警告 */
$warningHoverColor: #ff9600;
/* 危险 */
$dangerHoverColor: #f72239;

/********************** 基础颜色-激活 **********************/
/* 默认 */
$defaultActiveColor: #eee;
/* 主要 */
$primaryActiveColor: #0978e2;
/* 信息 */
$infoActiveColor: #6eb5fe;
/* 成功 */
$successActiveColor: #00ae64;
/* 警告 */
$warningActiveColor: #e38601;
/* 危险 */
$dangerActiveColor: #cb2537;

/********************** 基础颜色-相应谈色 **********************/
/* 默认 */
$defaultLightColor: #fff;
/* 主要 */
$primaryLightColor: #e1f3ff;
/* 信息 */
$infoLightColor: #6eb5fe;
/* 成功 */
$successLightColor: #d5fde5;
/* 警告 */
$warningLightColor: #fff3da;
/* 危险 */
$dangerLightColor: #ffe0e0;

/********************** 图标大小 **********************/
/* 最小尺寸图标 */
$iconSmallestSize: 14px;
/* 更小尺寸图标 */
$iconSmallerSize: 16px;
/* 小尺寸图标 */
$iconSmallSize: 20px;
/* 正常尺寸图标 */
$iconNormalSize: 22px;
/* 大尺寸图标 */
$iconLargeSize: 24px;
/* 最大尺寸图标 */
$iconLargerSize: 26px;

/********************** 文案 **********************/
/* 字体 */
$fnFamily: 'Microsoft Yahei';
/* 正常尺寸 */
$fnNormalSize: 14px;
/* 小尺寸 */
$fnSmallSize: 12px;
/* 大尺寸 */
$fnLargeSize: 16px;
/* 主要颜色 */
$fnMainColor: #63656e;
/* 次要颜色 用于 plaeholder 等 */
$fnMinorColor: #c3cdd7;

/********************** 边框 **********************/
/* 边框色 */
$borderColor: #c3cdd7;
$borderLightColor: #e6e6e6;
$borderInlineColor: #dde4eb;
/* 表单输入框色 */
$formBorderColor: #c4c6cc;
/* 表单输入框激活色 */
$formBorderFocusColor: #3c96ff;

/********************** 其它 **********************/
$defaultBackgroundColor: #fafafa;
$mainBorderColor: #ccc;
$subBorderColor: #ddd;
/* 滚动条背景色 */
$scrollbarColor: #dde4eb;

/********************** 层级管理 **********************/
/* 下拉菜单层 */
/* $dropdownMenuZIndex: 50; */
$dropdownMenuZIndex: 2500;
/* 下拉选框层 */
/* $selectorZIndex: 100; */
$selectorZIndex: 2500;
/* 日期选框层 */
/* $dateZIndex: 200; */
$dateZIndex: 2500;
/* 文字提示层 */
/* $tooltipZIndex: 500; */
$tooltipZIndex: 2500;
/* 确认提示层 */
/* $toolboxZIndex: 700; */
$toolboxZIndex: 2500;
/* 加载层 */
/* $loadingZIndex: 1000; */
$loadingZIndex: 100;
/* 消息提示层 */
/* $messageZIndex: 2000; */
$messageZIndex: 2500;
/* 侧弹层 */
/* $sideSliderZIndex: 1500; */
$sideSliderZIndex: 2500;
/* 对话框层 */
/* $dialogZIndex: 2000; */
$dialogZIndex: 2500;
