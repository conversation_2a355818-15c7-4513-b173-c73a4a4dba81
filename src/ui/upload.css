@import "./variable.css";
.bk-upload {
    img{
        width: 100%;
    }
    .pic-item {
        position: relative;
        display: inline-block;
        padding: 5px;
        height: 100px;
        width: 100px;
        border: 1px solid $newGreyColor;
        border-radius: 2px;
        vertical-align: top;
        overflow: hidden;
        &:not(:last-child) {
            margin-right: 10px;
        }
        &.fail {
            border-color: $newRedColor1;
            .error-image-icon {
                font-size: 28px;
                color: $newBlackColor3;
            }
        }
        &:hover {
            .mask {
                display: flex;
            }
        }
        .uploading-status {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 36px 10px;
            font-size: 14px;
            text-align: center;
            background: $defaultColor;
        }
        .fail-status {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 26px 10px;
            font-size: 14px;
            text-align: center;
            background: $defaultColor;
            .error-pic {
                font-size: 28px;
                color: $newBlackColor3;
            }
            .reupload {
                color: $newMainColor;
            }
            .delete-file {
                color: $newRedColor1;
                &:hover {
                    color: $newRedColor1;
                }
            }
        }
        .mask {
            display: none;
            position: absolute;
            left: 0;
            top: 0;
            width: 98px;
            height: 98px;
            justify-content: center;
            align-items: center;
            color: $defaultColor;
            background: rgba(0, 0, 0, .6);
            .fail-tips {
                width: 100%;
                height: 100%;
            }
        }
        .delete-file {
            position: absolute;
            right: 0;
            top: 0;
            color: $newGreyColor;
            font-size: 24px;
            cursor: pointer;
            z-index: 11;
            &:hover {
                color: $defaultColor;
            }
        }
    }
    .file-wrapper {
        position: relative;
        font-size: 14px;
        input[type=file] {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 10;
            cursor: pointer;
            opacity: 0;
        }
    }
    &.button {
        position: relative;
        .file-wrapper {
            width: 86px;
            height: 36px;
            border: 1px solid rgba(196, 198, 204, 1);
            opacity: 1;
            border-radius: 2px;
            cursor: pointer;
            margin-bottom: 10px;
            position: relative;
             :not(input) {
                display: none;
            }
            &::before {
                content: attr(bk-lableName);
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        p.tip {
            position: absolute;
            left: 100px;
            top: 0;
            margin: auto;
            height: 34px;
            display: flex;
            align-items: center;
        }
    }
    &.draggable {
        .file-wrapper {
            width: 100%;
            height: 80px;
            margin-bottom: 2px;
            border-radius: 2px;
            border: 1px dashed $newGreyColor;
            text-align: center;
            &:hover {
                border-color: $newMainColor;
                .upload-icon {
                    color: $newMainColor;
                }
            }
            &:focus {
                border-color: $newMainColor;
            }
            .upload-icon {
                display: inline-block;
                margin: 14px 0 2px;
                font-size: 28px;
                color: $newGreyColor;
            }
            .text-area {
                margin: 0;
                font-size: 12px;
            }
            .drop-upload {
                color: $newBlackColor2;
            }
            .click-upload {
                color: $newMainColor;
            }
        }
    }
    &.picture {
        .file-wrapper {
            display: inline-block;
            &:hover {
                .upload-btn {
                    color: $newMainColor;
                    border-color: $newMainColor;
                    i {
                        color: $newMainColor;
                    }
                }
                .uploaded-status .mask {
                    display: flex;
                }
            }
            &:focus {
                .upload-btn {
                    border-color: $newMainColor;
                }
            }
            .upload-btn {
                padding: 28px 0;
                width: 100px;
                height: 100px;
                border: 1px dashed $newGreyColor;
                border-radius: 2px;
                text-align: center;
                i {
                    margin-bottom: 2px;
                    font-size: 20px;
                    color: $newBlackColor3;
                }
            }
        }
    }
    .progress-bar-wrapper {
        margin-top: 4px;
        height: 2px;
        border-radius: 1px;
        background: $newGreyColor1;
        .progress-bar {
            height: 2px;
            width: 0%;
            background: $newGreenColor;
            border-radius: 1px;
        }
        .uploading {
            background: $newMainColor;
        }
        .success {
            background: $successColor;
        }
        .fail {
            background: $newRedColor1;
        }
    }
    .tip {
        margin: 5px 0 10px 0;
        font-size: 12px;
        text-align: left;
        color: $newBlackColor2;
    }
    .isdrag {
        border: 1px solid #0082ff;
    }
    .all-file {
        .file-item {
            position: relative;
            display: flex;
            margin-bottom: 10px;
            height: 60px;
            padding: 12px 10px;
            border-radius: 2px;
            border: 1px dashed $newGreyColor;
            background: rgba(250, 251, 253, 1);
            &.file-item-fail {
                border: 1px solid $newRedColor1;
                background: rgba(254, 221, 220, 0.4);
                .close-upload {
                    color: $newRedColor1;
                    &:hover {
                        color: $newRedColor1;
                    }
                }
            }
            .file-icon {
                flex: 0 0 36px;
                height: 36px;
                width: 36px;
                border-radius: 2px;
                i {
                    font-size: 36px;
                }
            }
            .close-upload {
                position: absolute;
                top: 0;
                right: 0;
                font-size: $iconLargeSize;
                color: $newGreyColor;
                cursor: pointer;
                z-index: 1;
            }
            .close-upload:hover {
                color: $fnMainColor;
            }
            .file-info {
                flex: 1;
                position: relative;
                margin-left: 10px;
                line-height: 1;
                font-size: 12px;
                overflow: hidden;
                .file-name {
                    display: inline-block;
                    font-size: 12px;
                    max-width: 250px;
                    line-height: 16px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .file-message {
                    position: absolute;
                    right: 25px;
                    display: inline-block;
                    font-size: 12px;
                    line-height: 1;
                    .upload-speed {
                        padding-right: 40px;
                    }
                    .done {
                        color: $newGreenColor;
                    }
                }
                .error-msg {
                    margin: 0;
                    height: 18px;
                    line-height: 18px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 12px;
                    color: $newRedColor1;
                }
            }
        }
    }
}
