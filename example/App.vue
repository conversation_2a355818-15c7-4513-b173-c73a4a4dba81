<!--
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 *
 * Terms of the MIT License:
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
-->

<template>
  <div id="app">
    <app-header
      root-domain="tencent.com"
      login-url="https://login.bk.tencent.com"
      avatar-host="https://q1.qlogo.cn"
      less-code-url="https://github.com/TencentBlueKing/bk-lesscode/blob/master/readme.md"
      design-url="https://bkdesign.bk.tencent.com"
      region="tencent">
    </app-header>
    <div class="app-container">
      <div class="app-side-nav">
        <side-nav></side-nav>
      </div>
      <div class="placeholder"></div>
      <div class="app-content">
        <router-view></router-view>
      </div>
    </div>
    <app-footer></app-footer>
  </div>
</template>

<script>
export default {
  name: 'app',
  data () {
    return {
      systemCls: 'mac'
    }
  },
  created () {
    const platform = window.navigator.platform.toLowerCase()
    if (platform.indexOf('win') === 0) {
      this.systemCls = 'win'
    }
    document.body.classList.add(this.systemCls)
  }
}
</script>

<style lang="postcss">
  @import './css/app.css';
  * {
    outline: none;
  }
</style>
