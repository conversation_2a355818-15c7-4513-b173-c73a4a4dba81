$defaultColor: #63656e;
$defaultHoverColor: #979ba5;
$defaultDisabledColor: #dcdee5;

$primaryColor: #3a84ff;
$primaryHoverColor: #699df4;
$primaryDisabledColor: #a3c5fd;

$successColor: #2dcb56;
$successHoverColor: #44e360;
$successDisabledColor: #94f5a4;

$warningColor: #ff9c01;
$warningHoverColor: #ffb849;
$warningDisabledColor: #ffd695;

$dangerColor: #ea3636;
$dangerHoverColor: #fe5657;
$dangerDisabledColor: #fd9c9c;

.bk-link {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    text-decoration: none;
    cursor: pointer;
    line-height: 20px;
    .bk-link-icon {
        font-size: 14px;
        &.is-left {
            margin-right: 4px;
        }
        &.is-right {
            margin-left: 4px;
        }
    }
    .bk-link-text {
        font-size: 14px;
    }
    &.is-default {
        color: $defaultColor;
        &:hover {
            color: $defaultHoverColor;
        }
        &.is-disabled {
            color: $defaultDisabledColor;
        }
    }
    &.is-primary {
        color: $primaryColor;
        &:hover {
            color: $primaryHoverColor;
        }
        &:active {
            color: #1768ef;
        }
        &:visited {
            color: $primaryHoverColor;
        }
        &.is-disabled {
            color: $primaryDisabledColor;
        }
    }
    &.is-success {
        color: $successColor;
        &:hover {
            color: $successHoverColor;
        }
        &:active {
            color: #1bb943;
        }
        &:visited {
            color: $successHoverColor;
        }
        &.is-disabled {
            color: $successDisabledColor;
        }
    }
    &.is-warning {
        color: $warningColor;
        &:hover {
            color: $warningHoverColor;
        }
        &:active {
            color: #eb9000;
        }
        &:visited {
            color: $warningHoverColor;
        }
        &.is-disabled {
            color: $warningDisabledColor;
        }
    }
    &.is-danger {
        color: $dangerColor;
        &:hover {
            color: $dangerHoverColor;
        }
        &:active {
            color: #da2726;
        }
        &:visited {
            color: $dangerHoverColor;
        }
        &.is-disabled {
            color: $dangerDisabledColor;
        }
    }
    &.is-disabled {
        cursor: not-allowed;
    }
    &.has-underline {
        text-decoration: underline;
    }
}
