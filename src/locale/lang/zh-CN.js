/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
*/

/**
 * @file 默认中文语言包
 *
 * Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved. 蓝鲸智云 版权所有
 */

export default {
  bk: {
    lang: 'zh-CN',
    datePicker: {
      // test: '我们{vari}hello {ccc}!@#$%^&&*({})',
      selectDate: '选择日期',
      selectTime: '选择时间',
      clear: '清除',
      ok: '确定',
      weekdays: {
        sun: '日',
        mon: '一',
        tue: '二',
        wed: '三',
        thu: '四',
        fri: '五',
        sat: '六'
      },
      hour: '时',
      min: '分',
      sec: '秒',
      toNow: '至今',
      now: '此刻'
    },
    dialog: {
      ok: '确定',
      cancel: '取消'
    },
    exception: {
      403: '无业务权限',
      404: '页面不存在',
      500: '服务维护中',
      building: '功能建设中',
      empty: '没有数据',
      searchEmpty: '搜索为空',
      login: '请登入蓝鲸'
    },
    form: {
      validPath: '请配置合法的路径'
    },
    input: {
      input: '请输入'
    },
    imageViewer: {
      loadFailed: '抱歉，图片加载失败',
      quitTips: 'ESC 可以退出全屏'
    },
    notify: {
      showMore: '查看更多'
    },
    select: {
      selectAll: '全选',
      pleaseselect: '请选择',
      searchPlaceholder: '输入关键字搜索',
      dataEmpty: '暂无选项',
      searchEmpty: '无匹配数据'
    },
    sideslider: {
      title: '标题'
    },
    tagInput: {
      placeholder: '请输入并按Enter结束'
    },
    transfer: {
      left: '左侧列表',
      total: '（共{total}条）',
      addAll: '全部添加',
      emptyContent: '无数据',
      right: '右侧列表',
      removeAll: '清空',
      emptySelected: '未选择任何项',
      searchPlaceholder: '请输入搜索关键字'
    },
    tree: {
      emptyText: '暂无数据'
    },
    steps: {
      step1: '步骤1',
      step2: '步骤2',
      step3: '步骤3'
    },
    uploadFile: {
      drag: '将文件拖到此处或',
      click: '点击上传',
      uploadDone: '上传完毕',
      uploading: '正在上传',
      reupload: '重新上传',
      replace: '点击替换',
      uploadFailed: '上传失败',
      fileExceedMsg: '{fileName} 文件不能超过 {size} MB',
      invalidFileName: '文件名不合法',
      invalidImageFormat: '只允许上传JPG|PNG|JPEG格式的图片',
      imageExceedMsg: '图片大小不能超过 {imgSize} MB',
      uploadLabel: '上传文件'
    },
    navigation: {
      headerTitle: '栏目名称'
    },
    searchSelect: {
      placeholder: '请输入',
      emptyText: '包含键值得过滤查询必须有一个值',
      condition: '或',
      remoteEmptyText: '查询无数据',
      remoteLoadingText: '正在加载中...',
      tips: '多个关键字用竖线 “|” 分隔',
      ok: '确认',
      cancel: '取消'
    },
    table: {
      emptyText: '暂无数据',
      sumText: '总计',
      setting: {
        title: '表格设置',
        fields: {
          title: '字段显示设置',
          subtitle: '（最多{max}项）',
          selectAll: '全选'
        },
        lineHeight: {
          title: '表格行高',
          small: '小',
          medium: '中',
          large: '大'
        },
        options: {
          ok: '确认',
          cancel: '取消'
        }
      },
      confirm: '确定',
      reset: '重置',
      all: '全部',
      filter: {
        placeholder: '请输入关键字',
        empty: '无匹配项'
      }
    },
    bigTree: {
      emptyText: '暂无搜索结果'
    },
    message: {
      copy: '复制',
      copied: '已复制',
      assistant: '助手',
      details: '详情',
      copySuccess: '复制成功',
      copyFailed: '复制失败'
    },
    image: {
      zoomIn: '放大',
      zoomOut: '缩小',
      rotateLeft: '向左旋转',
      rotateRight: '向右旋转',
      fullScreen: '适应屏幕',
      original: '快速回到 1：1'
    },
    versionDetail: {
      currentTagText: '当前版本'
    }
  }
}
