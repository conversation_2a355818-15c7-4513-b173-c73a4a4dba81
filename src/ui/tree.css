@import './variable.css';
@import './loading.css';

.fade-enter-active,
.fade-leave-active {
    transition: opacity .2s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0
}
.bk-tree {
    font-size: 14px;
    transition: .3s height ease-in-out, .3s padding-top ease-in-out, .3s padding-bottom ease-in-out;
    .expand-enter-active {
        transition: all 3s ease;
        height: 50px;
        overflow: hidden;
    }
    .expand-leave-active {
        transition: all 3s ease;
        height: 0px;
        overflow: hidden;
    }
    .expand-enter,
    .bk-tree .expand-leave {
        height: 0;
        opacity: 0;
    }
    ul {
        box-sizing: border-box;
        list-style-type: none;
        text-align: left;
        padding-left: 16px;
    }
    li {
        position: relative;
        margin: 0;
        list-style: none;
        list-style-type: none;
        text-align: left;
        &.leaf {
            padding-left: 16px;
        }
    }
    .tree-drag-node {
        line-height: 32px;
        .tree-expanded-icon {
            position: relative;
            color: #c0c4cc;
            cursor: pointer;
        }
        .tree-node {
            display: inline-block;
            height: 32px;
            font-size: 0;
        }
    }
    .loading {
        width: 14px;
        height: 14px;
        display: inline-block;
        top: -5px;
    }
    .node-title {
        display: inline-block;
        margin-left: 5px;
        border-radius: 3px;
        font-size: 14px;
        cursor: pointer;
    }
    .node-selected {
        color: $newMainColor;
    }
    .node-icon {
        display: inline-block;
        font-size: 14px;
    }

    /* checkbox */
    .bk-form-checkbox {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 14px;
        height: 14px;
        border: 1px solid $newBlackColor3;
        border-radius: 2px;

        input[type=checkbox] {
            display: none;
        }

        &:focus {
            border-color: $formBorderFocusColor;
            outline: none;
        }

        &--indeterminate {
            border-color: $newMainColor;
            background-color: $newMainColor;
            background-clip: content-box;

            &::after {
                content: '';
                display: block;
                width: 8px;
                height: 2px;
                background-color: #fff;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                border-radius: 2px;
            }
        }

        &--checked {
            border-color: $newMainColor;
            background-color: $newMainColor;
            background-clip: border-box;

            &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 3px;
                width: 4px;
                height: 8px;
                border: 2px solid #fff;
                border-left: 0;
                border-top: 0;
                transform-origin: center;
                transform: rotate(45deg) scaleY(1);
            }
        }

        &--disabled {
            border-color: $newGreyColor1;
            background-color: $newGreyColor1;
            cursor: not-allowed;

            &&--indeterminate ::after{
                background-color: $newGreyColor3;
            }

            &&--checked ::after{
                border-color: $newGreyColor3;
            }
        }
    }
}
.bk-tree > ul {
    padding-left: 0
}
.bk-has-border-tree {
    li:before {
        content: '';
        left: -8px;
        position: absolute;
        right: auto;
        border-width: 1px;
        border-left: 1px dashed $borderColor;
        bottom: 50px;
        height: 100%;
        left: -10px;
        top: -11px;
        width: 1px;
    }
    li:after {
        content: '';
        left: -10px;
        position: absolute;
        right: auto;
        border-width: 1px;
        border-top: 1px dashed $borderColor;
        height: 20px;
        top: 16px;
        width: 12px;
    }
    li:last-child::before {
        height: 26px;
    }
    li.leaf:after {
        content: '';
        left: -10px;
        position: absolute;
        right: auto;
        border-width: 1px;
        border-top: 1px dashed $borderColor;
        height: 20px;
        top: 15px;
        width: 24px;
    }
    li.single:before {
        top: -10px;
    }
    li.single:after {
        top: 16px;
    }
    li.leaf.single:after {
        top: 16px;
    }
}
.bk-has-border-tree > li.tree-first-node:before {
    top: 17px;
}
.bk-has-border-tree > li.tree-second-node:before {
    top: 4px;
}
.bk-has-border-tree > li.tree-first-node.tree-only-node::before {
    border-left: none;
}
.bk-has-border-tree > li.tree-only-node:after {
    border-top: none;
}
