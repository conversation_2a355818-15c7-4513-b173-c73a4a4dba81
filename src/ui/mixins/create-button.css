/**
 * @file create-button mixin
 *
 * usage:
 *      @mixin create-button red, green, green;
 */

@define-mixin create-button $color, $hover, $active {
    & {
        background: $color;
        border-color: $color;
        color: $defaultColor;
    }

    &:hover,
    &.hover {
        background-color: $hover;
        border-color: $hover;
        opacity: 1;
    }

    &:active,
    &.active {
        background-color: $active;
        border-color: $active;
    }

    &.is-disabled,
    &[disabled] {
        background-color: $newGreyColor1;
        border-color: $newGreyColor1;
        color: $defaultColor;
        cursor: not-allowed;
    }

    &.is-loading {
      cursor: default;
      &:hover,
      &.hover {
          background-color: $color;
          border-color: $color;
          opacity: 1;
      }
      &:active,
      &.active {
          background-color: $color;
          border-color: $color;
      }
      &.is-outline {
        .bk-button-loading {
          div {
            background-color: $color;
          }
        }
        &:hover,
        &.hover {
          .bk-button-loading {
            div {
              background-color: #fff;
            }
          }
        }
        &.active,
        &:active {
          .bk-button-loading {
            div {
              background-color: #fff;
            }
          }
        }
      }
    }

    &.is-outline {
        color: $color;
        border-color: $color;
        background-color: #fff;
        &:hover,
        &.hover {
            background-color: $color;
            border-color: $color;
            color: #fff;
        }
        &.active,
        &:active {
            background-color: $active;
            border-color: $active;
            color: #fff;
        }
    }

    /* &.is-selected {
        background-color: $active !important;
        border-color: $active !important;
        color: #fff !important;
        position: relative;
        z-index: 1;
        cursor: default;
    } */
}
