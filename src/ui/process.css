@import './variable.css';
@import "./loading.css";

.bk-process {
    display: flex;
    position: relative;
    .bk-icon {
        font-size: $iconNormalSize;
    }
    .bk-spin-loading {
        &.bk-spin-loading-mini {
            width: 22px;
            height: 22px;
            display: inline-flex;
            top: 2px;
            left: 3px;
        }
    }
    .bk-process-toggle {
        position: relative;
        left: 15px;
        width: 24px;
        height: 24px;
        line-height: 24px;
        border-radius: 50%;
        text-align: center;
        border: 1px solid $borderColor;
        font-size: 12px;
        color: $fnMainColor;
        text-decoration: none;
        &.active,
        &:hover {
            color: #fff;
            background: $newMainColor;
            border-color: $newMainColor;
        }
    }
    > ul {
        display: table;
        width: 100%;
        display: flex;
        -ms-display: flex;
        -moz-display: flex;
        -webkit-display: flex;
        margin: auto;
        padding: 0;
    }
    li {
        background-color: $newGreyColor2;
        display: table-cell;
        flex: 1;
        -ms-flex: 1;
        -moz-flex: 1;
        -webkit-flex: 1;
        text-align: center;
        height: 24px;
        line-height: 24px;
        position: relative;
        font-size: 12px;
        color: $fnMainColor;
        i {
            display: none;
        }
        .bk-process-item {
            display: inline-block;
            > .bk-icon {
                font-size: $iconSmallestSize;
            }
        }
        &:first-of-type {
            border-radius: 13px 0 0 13px;
        }
        &:last-of-type {
            border-radius:  0 13px 13px 0;
            &::before {
                display:none;
            }
            &::after {
                display:none;
            }
        }
        &::before {
            content:"";
            width: 0;
            height: 0;
            border: 12px solid;
            border-color: transparent transparent transparent #fff;
            position: absolute;
            right: -25px;
            top: 0;
            z-index: 1;
        }
        &::after {
            content:"";
            width: 0;
            height: 0;
            border: 12px solid;
            border-color: transparent transparent transparent $newGreyColor2;
            position: absolute;
            right: -23px;
            top: 0;
            z-index: 1;
        }
        .bk-process-step {
            color: $fnMainColor;
            font-size: 12px;
            dd {
                margin: 0;
                line-height: 32px;
            }
            i {
                display: none;
            }
            .success {
                i {
                    display: inline-block;
                    color: $newGreenColor;
                }
            }
            .steps-loading {
                top: 1px;
                left: 1px;
            }
            .step-item {
                &.error {
                    color: $newRedColor1;

                    .bk-icon {
                        color: $newRedColor1;
                        vertical-align: -2px;
                    }
                }
                &.done {
                    .bk-icon {
                        color: $newGreenColor;
                    }
                }
            }
        }

        &.success {
            background: $newGreenColor;
            color: #fff;
            &::after {
                border-color: transparent transparent transparent $newGreenColor;
            }
            & .bk-process-step {
                i {
                    color: $newGreenColor;
                }
            }
            i {
                display: inline-block;
            }
        }

        &.status-error {
            background: $newRedColor1;
            color: #fff;
            &::after {
                border-color: transparent transparent transparent $newRedColor1;
            }
            .bk-process-item > .bk-icon {
                display: inline-block;
            }
            .bk-process-step {
                .bk-icon {
                    display: inline-block;
                }
            }
        }
        &.status-done {
            background: $newGreenColor;
            color: #fff;
            &::after {
                border-color: transparent transparent transparent $newGreenColor;
            }
            .bk-process-item > .bk-icon {
                display: inline-block;
            }
            .bk-process-step {
                .bk-icon {
                    display: inline-block;
                }
            }
        }
        &.status-loading {
            .bk-process-item > .bk-icon {
                display: inline-block;
            }
            .bk-process-step {
                .bk-icon {
                    display: inline-block;
                }
            }
        }
        &.status-default {
            .bk-process-item > .bk-icon {
                display: inline-block;
            }
            .bk-process-step {
                .bk-icon {
                    display: inline-block;
                }
            }
        }

        &.current {
            background: $newMainColor;
            color: #fff;
            &::after {
                border-color: transparent transparent transparent $newMainColor;
            }
            >.bk-spin-loading {
                vertical-align: -3px;
            }
        }
    }
}
