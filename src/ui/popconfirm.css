
.bk-popconfirm-content {
    font-size: 12px;

    .popconfirm-split {
        display: inline-block;
        position: absolute;
        top: 9px;
        background-color: #dcdee5;
        width: 1px;
        height: 12px;
        margin-left: -2px;
    }
    &.popconfirm-more {
        padding: 12px 7px;
        .popconfirm-content {
            .title {
                font-size: 14px;
                color: #313238;
                padding-bottom: 10px;
            }
            .content {
                font-size: 12px;
                line-height: 20px;
                color: #63656e;
                padding-bottom: 16px;
            }
            .is-simple.content {
                font-size: 14px;
                line-height: 22px;
            }
        }
        .popconfirm-operate {
            text-align: right;
            margin-bottom: 2px;
        }
    }
    .popconfirm-operate {
        button {
            background: transparent;
            border: transparent;
            color: #3a84ff;
            margin-left: 4px;

            &:hover {
                color: #699df4;
            }
            &[type="button"] {
                border-radius: 2px;
                color: #63656e;
                border: 1px solid #c4c6cc;
                &:hover {
                    border-color: #979ba5;
                }
                &.primary {
                    color: #fff;
                    background: #3a84ff;
                    border: #3a84ff;
                    &:hover {
                        background-color: #699df4;
                        border-color: #699df4;
                    }
                }
            }
        }
        .default-operate-button[type="button"] {
            height: 26px;
            padding: 0 16px;
        }
    }
}
