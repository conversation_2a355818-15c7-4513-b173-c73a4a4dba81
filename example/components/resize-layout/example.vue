<!--
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community Edition):
 *
 *
 * Terms of the MIT License:
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
-->

<template>
  <div>
    <p>拖动中设置了disabled=true，不再执行handleMouseMove方法</p>
    <bk-resize-layout
      style="height: 500px;margin-top: 20px;"
      immediate
      :min="min"
      :disabled="disabled"
      @resizing="handleResizing"
      @after-resize="() => disabled = false">
      <div slot="aside">aside</div>
      <div slot="main">main</div>
    </bk-resize-layout>

    <!-- <bk-resize-layout collapsible :initial-divide="20" :max="420" style="height: 500px;" :auto-minimize="true" @collapse-change="handleCollapseChange">
      <template #aside>
        {{sss}}
      </template>
      <template #main>main</template>
    </bk-resize-layout> -->
  </div>
</template>

<script>
import { bkResizeLayout } from '@'

export default {
  name: 'demo',
  components: {
    bkResizeLayout
  },
  data () {
    return {
      disabled: false,
      min: 100,
      max: 400,
      sss: 'dddd'
    }
  },
  methods: {
    handleResizing (height) {
      console.log(height)
      this.disabled = (height - 3) <= this.min
    },

    handleCollapseChange (v) {
      this.sss = +new Date()
      console.error(v)
    }
  }
}
</script>
