/**
 * @file create-button-hover mixin
 *
 * usage:
 *      @mixin create-button-hover green, green;
 */

@define-mixin create-button-hover $hover, $active {
    & {
        background-color: $defaultColor;
        border-color: $newGreyColor;
        color: $newBlackColor2;
    }
    &:hover,
    &.hover {
        background-color: $hover;
        border-color: $hover;
        color: $defaultColor;
        opacity: 1;
    }

    &:active,
    &.active {
        background-color: $active;
        border-color: $active;
        color: $defaultColor;
    }

    &.is-disabled,
    &[disabled] {
        background-color: $defaultColor;
        border-color: $newGreyColor1;
        color: $newGreyColor;
        cursor: not-allowed;
    }

    &.is-loading {
        cursor: default;
        &:hover,
        &.hover {
            background-color: $defaultColor;
            border-color: $newGreyColor;
            opacity: 1;
        }
        &:active,
        &.active {
            background-color: $defaultColor;
            border-color: $newGreyColor;
        }
    }

    &.is-outline {
        color: $hover;
        border-color: $hover;
        background-color: $defaultColor;
        &:hover,
        &.hover {
            background-color: $hover;
            border-color: $hover;
            color: $defaultColor;
        }
        &.active,
        &:active {
            background-color: $active;
            border-color: $active;
            color: $defaultColor;
        }
    }
}
