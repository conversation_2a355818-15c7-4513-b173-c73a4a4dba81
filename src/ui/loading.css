@import './variable.css';

@define-mixin create-rotate $time, $deg {
    animation-delay: $time;
    transform: rotate($deg);
}

.bk-loading {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: $loadingZIndex;
    .bk-loading-wrapper {
        text-align: center;
        line-height: 1;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
    }
    &-title {
        text-align: center;
        font-size: 14px;
        color: $fnMainColor;
        line-height: initial;
        margin-top: 5px;
    }
    .bk-loading1 {
        position: relative;
        width: 75px;
        height: 14px;
        margin: auto;
        display: inline-block;
        .point {
            position: absolute;
            top: 0;
            width: 14px;
            height: 14px;
            animation-name: scale-animate;
            animation-duration: .8s;
            animation-iteration-count: infinite;
            animation-direction: normal;
            transform: scale(.6);
            border-radius: 19px;
        }

        .point1 {
            background-color: #fd6154;
            left: 0;
            animation-delay: 0.1s;
        }

        .point2 {
            background-color: #ffb726;
            left: 20px;
            animation-delay: 0.25s;
        }

        .point3 {
            background-color: #4cd084;
            left: 40px;
            animation-delay: 0.4s;
        }

        .point4 {
            background-color: #57a3f1;
            left: 60px;
            animation-delay: 0.55s;
        }
    }
}

/* 加载动画一，点状 */
.bk-dot-loading,
.bk-loading1 {
    &.bk-size-mini {
        width: 30px;

        .point {
            width: 6px;
            height: 6px;
        }

        .point1 {
            left: 0;
        }

        .point2 {
            left: 10px;
        }

        .point3 {
            left: 20px;
        }

        .point4 {
            left: 30px;
        }
    }

    &.bk-size-small {
        width: 45px;

        .point {
            width: 10px;
            height: 10px;
        }

        .point1 {
            left: 0;
        }

        .point2 {
            left: 15px;
        }

        .point3 {
            left: 30px;
        }

        .point4 {
            left: 45px;
        }
    }

    &.bk-black {
        .point1,
        .point2,
        .point3,
        .point4 {
            background: #979ba5;
        }
    }

    &.bk-primary {
        .point1,
        .point2,
        .point3,
        .point4 {
            background: $primaryColor;
        }
    }

    &.bk-danger {
        .point1,
        .point2,
        .point3,
        .point4 {
            background: $dangerColor;
        }
    }

    &.bk-warning {
        .point1,
        .point2,
        .point3,
        .point4 {
            background: $warningColor;
        }
    }
    &.bk-default {
        .point1,
        .point2,
        .point3,
        .point4 {
            background: #979BA5;
        }
    }
}

@keyframes scale-animate {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(.6);
    }
}

@-webkit-keyframes scale-animate {
    0% {
        -webkit-transform: scale(1);
    }

    100% {
        -webkit-transform: scale(.6);
    }
}

.bk-spin-loading,
.bk-loading2 {
    width: 48px;
    height: 48px;
    position: relative;
    margin: auto;
    display:inline-block;
    vertical-align: middle;
    display: flex;
    justify-content: center;
    .rotate {
        position: absolute;
        /* top: 77%; */
        /* right: 43%; */
        /* background-color: transparent; */
        /* background-color: #39424e\9\0; */
        width: 6px;
        height: 8px;
        transform-origin: 50% 24px;
        border-radius: 8px;
        transform: scale(0.4);
        animation-name: fade;
        animation-duration: 1.2s;
        animation-iteration-count: infinite;
        animation-direction: normal;
    }
    &.bk-spin-loading-success {
        .rotate {
            /* background: #30d878; */
            animation-name: fadeSuccess;
        }
    }
    &.bk-spin-loading-danger {
        .rotate {
            /* background:  #ff5656; */
            animation-name: fadeDanger;
        }
    }
    &.bk-spin-loading-warning {
        .rotate {
            /* background: #ffb400; */
            animation-name: fadeWarning;
        }
    }
    &.bk-spin-loading-primary {
        .rotate {
            /* background-color: #3c96ff; */
            animation-name: fadePrimary;
        }
    }

    &.bk-spin-loading-white {
        .rotate {
            /* background: #fff; */
            animation-name: fadeWhite;
        }
    }

    .rotate1 {
        @mixin create-rotate 0.45s, -90deg;
    }
    .rotate2 {
        @mixin create-rotate 0.6s, -45deg;
    }
    .rotate3 {
        @mixin create-rotate 0.75s, 0deg;
    }
    .rotate4 {
        @mixin create-rotate 0.9s, 45deg;
    }
    .rotate5 {
        @mixin create-rotate 1.05s, 90deg;
    }
    .rotate6 {
        @mixin create-rotate 1.2s, 135deg;
    }
    .rotate7 {
        @mixin create-rotate 1.35s, 180deg;
    }
    .rotate8 {
        @mixin create-rotate 1.5s, -135deg;
    }
    &.bk-spin-loading-large {
        width: 68px;
        height: 68px;
        .rotate {
            width: 10px;
            height: 12px;
            border-radius: 12px;
            transform-origin: 50% 34px;
        }
    }
    &.bk-spin-loading-small {
        width: 32px;
        height: 32px;
        .rotate {
            width: 4px;
            height: 5px;
            border-radius: 5px;
            transform-origin: 50% 16px;
        }
    }
    &.bk-spin-loading-mini {
        width: 16px;
        height: 16px;
        .rotate {
            width: 2px;
            height: 3px;
            border-radius: 3px;
            transform-origin: 50% 8px;
        }
    }
}

@keyframes fade {
    0% {
        background-color: #39424e;
    }

    100% {
        background-color: transparent;
    }
}
@-webkit-keyframes fade {
    0% {
        background-color: #39424e;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeSuccess {
    0% {
        background-color: $successColor;
    }

    100% {
        background-color: transparent;
    }
}

@-webkit-keyframes fadeSuccess {
    0% {
        background-color: $successColor;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadePrimary {
    0% {
        background-color: $primaryColor;
    }

    100% {
        background-color: transparent;
    }
}

@-webkit-keyframes fadePrimary {
    0% {
        background-color: $primaryColor;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeDanger {
    0% {
        background-color: $dangerColor;
    }

    100% {
        background-color: transparent;
    }
}

@-webkit-keyframes fadeDanger {
    0% {
        background-color: $dangerColor;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeWarning {
    0% {
        background-color: $warningColor;
    }

    100% {
        background-color: transparent;
    }
}

@-webkit-keyframes fadeWarning {
    0% {
        background-color: $warningColor;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeWhite {
    0% {
        background-color: rgb(255, 255, 255);
    }

    100% {
        background-color: transparent;
    }
}

@-webkit-keyframes fadeWhite {
    0%{
        background-color: rgb(255, 255, 255);
    }

    100%{
        background-color: transparent;
    }
}

/* 加载动画容器，.bk-loading 保留，为了兼容 */
.bk-loading-box,
.bk-loading {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: $loadingZIndex;
    .bk-loading-wrapper {
        text-align: center;
        line-height: 1;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    &-title {
        text-align: center;
        font-size: 14px;
        color: $fnMainColor;
        line-height: initial;
        margin-top: 5px;
    }
}

@keyframes fade {
    0% {
        background-color: #39424e;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeSuccess {
    0% {
        background-color: #30d878;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadePrimary {
    0% {
        background-color: #3c96ff;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeDanger {
    0% {
        background-color: #ff5656;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeWarning {
    0% {
        background-color: #ffb400;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes fadeWhite {
    0% {
        background-color: white;
    }

    100% {
        background-color: transparent;
    }
}

@keyframes animate {
    0% {
        transform: scale(.5)
    }

    100% {
        transform: scale(.3)
    }
}

.bk-default-loading {
    display: inline-block;
    .bk-loading-wrapper {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: absolute;
        width: 100%;
        height: 100%;
        left: unset;
        top: 0;
        transform: unset;
        -webkit-transform: unset;
        background: inherit;
        padding: 0 25%;

        & :nth-child(1) {
            margin: unset;
          }
    }
}
