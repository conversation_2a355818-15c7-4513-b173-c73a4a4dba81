@font-face {
	font-family: "bk";
	src: url("fonts/iconcool.svg#iconcool") format("svg"),
url("fonts/iconcool.ttf") format("truetype"),
url("fonts/iconcool.woff") format("woff"),
url("fonts/iconcool.eot?#iefix") format("embedded-opentype");
    font-weight: normal;
    font-style: normal;
}

.bk-icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'bk' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  text-align: center;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-angle-double-down:before {
	content: "\e101";
}
.icon-angle-double-left:before {
	content: "\e102";
}
.icon-angle-double-right:before {
	content: "\e103";
}
.icon-angle-double-up:before {
	content: "\e104";
}
.icon-angle-left:before {
	content: "\e105";
}
.icon-angle-down:before {
	content: "\e106";
}
.icon-angle-right:before {
	content: "\e107";
}
.icon-angle-up:before {
	content: "\e108";
}
.icon-apps-shape:before {
	content: "\e109";
}
.icon-apps:before {
	content: "\e10a";
}
.icon-area-chart:before {
	content: "\e10b";
}
.icon-arrows-down-circle-shape:before {
	content: "\e10c";
}
.icon-arrows-down-circle:before {
	content: "\e10d";
}
.icon-arrows-down-shape:before {
	content: "\e10e";
}
.icon-arrows-down:before {
	content: "\e10f";
}
.icon-arrows-left-circle-shape:before {
	content: "\e110";
}
.icon-arrows-left-circle:before {
	content: "\e111";
}
.icon-arrows-left-shape:before {
	content: "\e112";
}
.icon-arrows-left:before {
	content: "\e113";
}
.icon-arrows-m-down-shape:before {
	content: "\e114";
}
.icon-arrows-m-left-shape:before {
	content: "\e115";
}
.icon-arrows-m-right-shape:before {
	content: "\e116";
}
.icon-arrows-m-up-shape:before {
	content: "\e117";
}
.icon-arrows-right-circle-shape:before {
	content: "\e118";
}
.icon-arrows-right-circle:before {
	content: "\e119";
}
.icon-arrows-right-shape:before {
	content: "\e11a";
}
.icon-arrows-right:before {
	content: "\e11b";
}
.icon-arrows-up-circle-shape:before {
	content: "\e11c";
}
.icon-arrows-up-circle:before {
	content: "\e11d";
}
.icon-arrows-up-shape:before {
	content: "\e11e";
}
.icon-arrows-up:before {
	content: "\e11f";
}
.icon-back-shape:before {
	content: "\e120";
}
.icon-back:before {
	content: "\e121";
}
.icon-back2:before {
	content: "\e122";
}
.icon-bar-chart:before {
	content: "\e123";
}
.icon-bk:before {
	content: "\e124";
}
.icon-block-shape:before {
	content: "\e125";
}
.icon-calendar-shape:before {
	content: "\e126";
}
.icon-calendar:before {
	content: "\e127";
}
.icon-chain:before {
	content: "\e128";
}
.icon-check-1:before {
	content: "\e129";
}
.icon-check-circle-shape:before {
	content: "\e12a";
}
.icon-check-circle:before {
	content: "\e12b";
}
.icon-circle-2-1:before {
	content: "\e12c";
}
.icon-circle-4-1:before {
	content: "\e12d";
}
.icon-circle-shape:before {
	content: "\e12e";
}
.icon-circle:before {
	content: "\e12f";
}
.icon-clipboard-shape:before {
	content: "\e130";
}
.icon-clipboard:before {
	content: "\e131";
}
.icon-clock-shape:before {
	content: "\e132";
}
.icon-clock:before {
	content: "\e133";
}
.icon-close-circle-shape:before {
	content: "\e134";
}
.icon-close-circle:before {
	content: "\e135";
}
.icon-close:before {
	content: "\e136";
}
.icon-close3-shape:before {
	content: "\e137";
}
.icon-code:before {
	content: "\e138";
}
.icon-cog-shape:before {
	content: "\e139";
}
.icon-cog:before {
	content: "\e13a";
}
.icon-cry-shape:before {
	content: "\e13b";
}
.icon-cry:before {
	content: "\e13c";
}
.icon-dashboard-2-shape:before {
	content: "\e13d";
}
.icon-dashboard-2:before {
	content: "\e13e";
}
.icon-dashboard-shape:before {
	content: "\e13f";
}
.icon-dashboard:before {
	content: "\e140";
}
.icon-data-shape:before {
	content: "\e141";
}
.icon-data:before {
	content: "\e142";
}
.icon-data2-shape:before {
	content: "\e143";
}
.icon-data2:before {
	content: "\e144";
}
.icon-dedent:before {
	content: "\e145";
}
.icon-delete:before {
	content: "\e146";
}
.icon-dialogue-empty-shape:before {
	content: "\e147";
}
.icon-dialogue-empty:before {
	content: "\e148";
}
.icon-dialogue-shape:before {
	content: "\e149";
}
.icon-dialogue:before {
	content: "\e14a";
}
.icon-dispirited-shape:before {
	content: "\e14b";
}
.icon-dispirited:before {
	content: "\e14c";
}
.icon-docker:before {
	content: "\e14d";
}
.icon-down-shape:before {
	content: "\e14e";
}
.icon-download:before {
	content: "\e14f";
}
.icon-edit:before {
	content: "\e150";
}
.icon-edit2:before {
	content: "\e151";
}
.icon-ellipsis:before {
	content: "\e152";
}
.icon-email-shape:before {
	content: "\e153";
}
.icon-email:before {
	content: "\e154";
}
.icon-empty-shape:before {
	content: "\e155";
}
.icon-empty:before {
	content: "\e156";
}
.icon-end:before {
	content: "\e157";
}
.icon-exclamation-circle-shape:before {
	content: "\e158";
}
.icon-exclamation-circle:before {
	content: "\e159";
}
.icon-exclamation-triangle-shape:before {
	content: "\e15a";
}
.icon-exclamation-triangle:before {
	content: "\e15b";
}
.icon-exclamation:before {
	content: "\e15c";
}
.icon-execute:before {
	content: "\e15d";
}
.icon-eye-shape:before {
	content: "\e15e";
}
.icon-eye-slash-shape:before {
	content: "\e15f";
}
.icon-eye-slash:before {
	content: "\e160";
}
.icon-eye:before {
	content: "\e161";
}
.icon-file-plus-shape:before {
	content: "\e162";
}
.icon-file-plus:before {
	content: "\e163";
}
.icon-file-shape:before {
	content: "\e164";
}
.icon-file:before {
	content: "\e165";
}
.icon-folder-open-shape:before {
	content: "\e166";
}
.icon-folder-open:before {
	content: "\e167";
}
.icon-folder-plus-shape:before {
	content: "\e168";
}
.icon-folder-plus:before {
	content: "\e169";
}
.icon-folder-shape:before {
	content: "\e16a";
}
.icon-folder:before {
	content: "\e16b";
}
.icon-full-screen:before {
	content: "\e16c";
}
.icon-heart-shape:before {
	content: "\e16d";
}
.icon-heart:before {
	content: "\e16e";
}
.icon-hide:before {
	content: "\e16f";
}
.icon-home-shape:before {
	content: "\e170";
}
.icon-home:before {
	content: "\e171";
}
.icon-id-shape:before {
	content: "\e172";
}
.icon-id:before {
	content: "\e173";
}
.icon-image-shape:before {
	content: "\e174";
}
.icon-image:before {
	content: "\e175";
}
.icon-indent:before {
	content: "\e176";
}
.icon-info-circle-shape:before {
	content: "\e177";
}
.icon-info-circle:before {
	content: "\e178";
}
.icon-info:before {
	content: "\e179";
}
.icon-key:before {
	content: "\e17a";
}
.icon-left-shape:before {
	content: "\e17b";
}
.icon-line-chart:before {
	content: "\e17c";
}
.icon-list:before {
	content: "\e17d";
}
.icon-lock-shape:before {
	content: "\e17e";
}
.icon-lock:before {
	content: "\e17f";
}
.icon-minus-circle-shape:before {
	content: "\e180";
}
.icon-minus-circle:before {
	content: "\e181";
}
.icon-minus-square-shape:before {
	content: "\e182";
}
.icon-minus-square:before {
	content: "\e183";
}
.icon-minus:before {
	content: "\e184";
}
.icon-mobile-shape:before {
	content: "\e185";
}
.icon-mobile:before {
	content: "\e186";
}
.icon-monitors-cog:before {
	content: "\e187";
}
.icon-monitors:before {
	content: "\e188";
}
.icon-more:before {
	content: "\e189";
}
.icon-move:before {
	content: "\e18a";
}
.icon-next-shape:before {
	content: "\e18b";
}
.icon-next:before {
	content: "\e18c";
}
.icon-order-shape:before {
	content: "\e18d";
}
.icon-order:before {
	content: "\e18e";
}
.icon-panel-permission:before {
	content: "\e18f";
}
.icon-panel-shape:before {
	content: "\e190";
}
.icon-panel:before {
	content: "\e191";
}
.icon-panels:before {
	content: "\e192";
}
.icon-password-shape:before {
	content: "\e193";
}
.icon-password:before {
	content: "\e194";
}
.icon-pause:before {
	content: "\e195";
}
.icon-pc-shape:before {
	content: "\e196";
}
.icon-pc:before {
	content: "\e197";
}
.icon-pie-chart-shape:before {
	content: "\e198";
}
.icon-pie-chart:before {
	content: "\e199";
}
.icon-pipeline-shape:before {
	content: "\e19a";
}
.icon-pipeline:before {
	content: "\e19b";
}
.icon-play-circle-shape:before {
	content: "\e19c";
}
.icon-play-shape:before {
	content: "\e19d";
}
.icon-play:before {
	content: "\e19e";
}
.icon-play2:before {
	content: "\e19f";
}
.icon-play3:before {
	content: "\e1a0";
}
.icon-plus-circle-shape:before {
	content: "\e1a1";
}
.icon-plus-circle:before {
	content: "\e1a2";
}
.icon-plus-square-shape:before {
	content: "\e1a3";
}
.icon-plus-square:before {
	content: "\e1a4";
}
.icon-plus:before {
	content: "\e1a5";
}
.icon-project:before {
	content: "\e1a6";
}
.icon-qq-shape:before {
	content: "\e1a7";
}
.icon-qq:before {
	content: "\e1a8";
}
.icon-question-circle-shape:before {
	content: "\e1a9";
}
.icon-question-circle:before {
	content: "\e1aa";
}
.icon-question:before {
	content: "\e1ab";
}
.icon-refresh:before {
	content: "\e1ac";
}
.icon-right-shape:before {
	content: "\e1ad";
}
.icon-rtx:before {
	content: "\e1ae";
}
.icon-save-shape:before {
	content: "\e1af";
}
.icon-save:before {
	content: "\e1b0";
}
.icon-script-file:before {
	content: "\e1b1";
}
.icon-script-files:before {
	content: "\e1b2";
}
.icon-search:before {
	content: "\e1b3";
}
.icon-sina-shape:before {
	content: "\e1b4";
}
.icon-sina:before {
	content: "\e1b5";
}
.icon-sitemap-shape:before {
	content: "\e1b6";
}
.icon-sitemap:before {
	content: "\e1b7";
}
.icon-smile-shape:before {
	content: "\e1b8";
}
.icon-smile:before {
	content: "\e1b9";
}
.icon-sort:before {
	content: "\e1ba";
}
.icon-star-shape:before {
	content: "\e1bb";
}
.icon-star:before {
	content: "\e1bc";
}
.icon-stop-shape:before {
	content: "\e1bd";
}
.icon-stop:before {
	content: "\e1be";
}
.icon-tree-application-shape:before {
	content: "\e1bf";
}
.icon-tree-application:before {
	content: "\e1c0";
}
.icon-tree-group-shape:before {
	content: "\e1c1";
}
.icon-tree-group:before {
	content: "\e1c2";
}
.icon-tree-module-shape:before {
	content: "\e1c3";
}
.icon-tree-module:before {
	content: "\e1c4";
}
.icon-tree-process-shape:before {
	content: "\e1c5";
}
.icon-tree-process:before {
	content: "\e1c6";
}
.icon-un-full-screen:before {
	content: "\e1c8";
}
.icon-unlock-shape:before {
	content: "\e1c7";
}
.icon-unlock:before {
	content: "\e1c9";
}
.icon-up-shape:before {
	content: "\e1ca";
}
.icon-upload:before {
	content: "\e1cb";
}
.icon-user-shape:before {
	content: "\e1cc";
}
.icon-user:before {
	content: "\e1cd";
}
.icon-weixin-shape:before {
	content: "\e1ce";
}
.icon-weixin:before {
	content: "\e1cf";
}
.icon-work-manage:before {
	content: "\e1d0";
}
.icon-funnel:before {
	content: "\e1d1";
}
.icon-user-group:before {
	content: "\e1d2";
}
.icon-user-3:before {
	content: "\e1d3";
}
.icon-copy:before {
	content: "\e1d4";
}
.icon-batch-edit-line:before {
	content: "\e1d6";
}
.icon-refresh-line:before {
	content: "\e1d5";
}
.icon-close-line:before {
	content: "\e1d7";
}
.icon-1_up:before {
	content: "\e1d8";
}
.icon-arrows-right--line:before {
	content: "\e1db";
}
.icon-arrows-left-line:before {
	content: "\e1d9";
}
.icon-arrows-down-line:before {
	content: "\e1dc";
}
.icon-arrows-up-line:before {
	content: "\e1da";
}
.icon-angle-double-right-line:before {
	content: "\e1e1";
}
.icon-angle-double-down-line:before {
	content: "\e1de";
}
.icon-angle-double-up-line:before {
	content: "\e1df";
}
.icon-angle-double-left-line:before {
	content: "\e1dd";
}
.icon-angle-left-line:before {
	content: "\e1e0";
}
.icon-angle-right-line:before {
	content: "\e1e2";
}
.icon-angle-up-line:before {
	content: "\e1e4";
}
.icon-angle-down-line:before {
	content: "\e1e3";
}
.icon-check-line:before {
	content: "\e1ed";
}
.icon-close-line-2:before {
	content: "\e1ec";
}
.icon-edit-line:before {
	content: "\e1ee";
}
.icon-list-line:before {
	content: "\e1eb";
}
.icon-plus-line:before {
	content: "\e1ef";
}
.icon-angle-up-fill:before {
	content: "\e1f0";
}
.icon-angle-down-fill:before {
	content: "\e1f1";
}
.icon-grag-fill:before {
	content: "\e1f2";
}
.icon-template-fill-49:before {
	content: "\e1f3";
}
.icon-folder-fill:before {
	content: "\e1f4";
}
.icon-expand-line:before {
	content: "\e1f5";
}
.icon-shrink-line:before {
	content: "\e1f6";
}
.icon-minus-line:before {
	content: "\e1f7";
}
.icon-compressed-file:before {
	content: "\e1f8";
}
.icon-upload-cloud:before {
	content: "\e1fa";
}
.icon-text-file:before {
	content: "\e1f9";
}
.icon-filliscreen-line:before {
	content: "\e1fe";
}
.icon-left-turn-line:before {
	content: "\e1fb";
}
.icon-right-turn-line:before {
	content: "\e1fc";
}
.icon-enlarge-line:before {
	content: "\e1fd";
}
.icon-narrow-line:before {
	content: "\e1ff";
}
.icon-unfull-screen:before {
	content: "\e200";
}
.icon-image:before {
	content: "\e203";
}
.icon-image-fail:before {
	content: "\e204";
}
.icon-normalized:before {
	content: "\e205";
}
.icon-chinese:before {
	content: "\e206";
}
.icon-english:before {
	content: "\e207";
}
.icon-japanese:before {
	content: "\e208";
}
.icon-date-fill:before {
	content: "\e209";
}
.icon-time:before {
	content: "\e20a";
}
.icon-data-line:before {
	content: "\e20b";
}
.icon-arrow-up:before {
	content: "\e213";
}
.icon-arrow-down:before {
	content: "\e214";
}
.icon-qiehuan:before {
	content: "\e20c";
}
.icon-loading-tubiao:before {
	content: "\e20d";
}
.icon-weixin-pro:before {
	content: "\e20f";
}
.icon-copy-shape:before {
	content: "\e210";
}
.icon-fix-line:before {
	content: "\e211";
}
.icon-fix-shape:before {
	content: "\e212";
}
.icon-arrow-left:before {
	content: "\e215";
}
.icon-arrow-right:before {
	content: "\e216";
}
.icon-assistant:before {
	content: "\e217";
}
