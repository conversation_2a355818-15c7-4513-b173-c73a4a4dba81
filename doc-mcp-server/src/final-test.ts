#!/usr/bin/env node

import { createBKUIDocServer } from "./server.js";
import path from "path";

async function finalTest() {
  console.log("🎯 Final Test for BKUI Documentation MCP Server...\n");

  // 获取组件路径
  const componentsPath = path.resolve(process.cwd(), '../example/components');
  console.log(`Components path: ${componentsPath}`);

  const server = createBKUIDocServer(componentsPath);

  try {
    // 获取服务器信息
    console.log("\n📋 Server Info:");
    const serverInfo = server.getServerInfo();
    console.log(`  Name: ${serverInfo.name}`);
    
    // 获取工具列表
    console.log("\n🔧 Available Tools:");
    const toolInfo = server.getToolListInfo();
    toolInfo.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });

    // 直接测试工具执行
    console.log("\n🚀 Testing Tool Execution:");
    
    try {
      console.log("1. Testing executeTool method...");
      const listResult = await server.executeTool('componentList', { includeDescription: true });
      console.log(`   ✅ componentList returned:`, typeof listResult, listResult ? Object.keys(listResult) : 'null');
      
      if (listResult && listResult.total) {
        console.log(`   📊 Found ${listResult.total} components`);
        if (listResult.components && listResult.components.length > 0) {
          console.log(`   📝 First component: ${listResult.components[0].name}`);
        }
      }
    } catch (error) {
      console.log(`   ❌ Error executing componentList: ${error}`);
    }

    try {
      console.log("\n2. Testing search tool...");
      const searchResult = await server.executeTool('componentSearch', { query: 'button', limit: 3 });
      console.log(`   ✅ componentSearch returned:`, typeof searchResult, searchResult ? Object.keys(searchResult) : 'null');
      
      if (searchResult && searchResult.total) {
        console.log(`   🔍 Found ${searchResult.total} matching components`);
      }
    } catch (error) {
      console.log(`   ❌ Error executing componentSearch: ${error}`);
    }

    try {
      console.log("\n3. Testing guide tool...");
      const guideResult = await server.executeTool('componentGuide', { 
        componentName: 'button', 
        sections: ['properties'] 
      });
      console.log(`   ✅ componentGuide returned:`, typeof guideResult, guideResult ? Object.keys(guideResult) : 'null');
      
      if (guideResult && guideResult.component) {
        console.log(`   📖 Component: ${guideResult.component.displayName}`);
        console.log(`   📋 Properties: ${guideResult.properties?.length || 0}`);
      }
    } catch (error) {
      console.log(`   ❌ Error executing componentGuide: ${error}`);
    }

    console.log("\n✅ Final test completed!");

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await server.close();
  }
}

// 运行测试
finalTest().catch(console.error);
