
.bk-virtual-render {
    position: relative;

		&.bk-virtual-content,
		.bk-virtual-content {
      position: absolute;
      left: 0;
      top: 0;
			bottom: 0;
      width: 100%;
      height: 100%;
    }

    .bk-virtual-section {
      background: transparent;
      width: 1px;
    }

         
    &.bk-scroll-x {
      overflow-x: auto;
      scrollbar-color: #a0a0a0 transparent;
      scrollbar-width: thin;
      &::-webkit-scrollbar {
        height: 8px;
        background-color: transparent;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: #a0a0a0;
      }
    }
    
    &.bk-scroll-y {
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 8px;
        background-color: transparent;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: #dcdee5;
      }
    }
  }
 