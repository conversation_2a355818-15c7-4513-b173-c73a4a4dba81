@import './variable.css';

.bk-form-checkbox {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 0;
  line-height: 18px;
  overflow: hidden;
  cursor: pointer;

  &.is-indeterminate:not(.is-checked) {
    .bk-checkbox {
      border-color: $newMainColor;
      background-color: $newMainColor;
      background-clip: content-box;
      position: relative;

      &::after {
        content: '';
        width: 8px;
        height: 2px;
        background: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -4px;
        border-radius: 2px;
        margin-top: -1px;
        display: inline-block;
      }
    }
  }

  &.is-disabled {
    cursor: not-allowed;

    .bk-checkbox {
      border-color: $newGreyColor1;
      background-color: $newGreyColor3;
    }

    /* .bk-checkbox-text {
            color: $newGreyColor;
        } */
  }

  &.is-disabled.is-checked {
    .bk-checkbox {
      border-color: $newGreyColor1;
      background-color: $newGreyColor1;
    }
  }

  &.is-checked {
    .bk-checkbox {
      border-color: $newMainColor;
      background-color: $newMainColor;
      background-clip: border-box;

      &:after {
        content: '';
        position: absolute;
        top: 1px;
        left: 4px;
        width: 4px;
        height: 8px;
        border: 2px solid #fff;
        border-left: 0;
        border-top: 0;
        transform-origin: center;
        transform: rotate(45deg) scaleY(1);
      }
    }
  }

  .bk-checkbox {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    border: 1px solid $newBlackColor3;
    border-radius: 2px;
    &:focus {
      border-color: $formBorderFocusColor;
      outline: none;
    }
  }

  .bk-checkbox-text {
    display: inline-block;
    margin: 0 0 0 6px;
    vertical-align: middle;
    font-size: 14px;
    color: $newBlackColor2;
  }
}
