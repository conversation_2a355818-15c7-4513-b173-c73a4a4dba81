@import './variable.css';
.bk-switcher {
    /* 默认尺寸 */
    width: 36px;
    height: 20px;
    line-height: 20px;
    position: relative;
    border-radius: 50px;
    display: inline-block;
    vertical-align: middle;
    transition: all .4s ease;
    background-color: $newGreyColor;
    text-align: left;
    &:focus {
        box-shadow:0px 0px 6px $newGreyColor;
    }
    &:after {
        left: 2px;
        top: 2px;
        content: "";
        height: 24px;
        width: 24px;
        border-radius: 100px;
        display: block;
        transition: all .3s ease;
        position: absolute;
        cursor: pointer;
        background: $defaultColor;
        box-shadow: 0 0 3px rgba(0, 0, 0, .2)
    }
    &:not(.show-label):after {
        height: 16px;
        width: 16px;
    }
    &:not(.show-label).is-checked {
        &:after {
            margin-left: -18px;
        }
    }
    &.is-loading {
        cursor: defalt;
        input[type=checkbox] {
            cursor: default;
        }
    }

    .bk-switcher-loading {
        width: 18px;
        position: absolute;
        z-index: 10;
        top: 50%;
        left: 1px;
        transform: translateY(-50%);
    }

    .switcher-label {
        width: 30px;
        height: 28px;
        font-size: 12px;
        color: $defaultColor;
        transition: all ease 0.3s;
        font-weight: normal;
        display: none;
        margin: 0 0 0 26px;
        text-align: center;
        vertical-align: top;
        .on-text {
            display: none;
        }
        .off-text {
            display: inline-block
        }
    }

    /* 显示文本时固定尺寸 */
    &.show-label {
        width: 60px;
        height: 28px;
        line-height: 28px;
        .switcher-label {
            display: inline-block;
        }

        .bk-switcher-loading {
            width: 26px;
        }
    }

    &.is-disabled {
        cursor: default;
        /* background-color: #eee !important; */
        opacity: 0.3;
        input[type=checkbox] {
            cursor: not-allowed;
        }
    }
    &.is-checked {
        background: $newGreenColor;
        &:focus {
            box-shadow:0px 0px 6px $newGreenColor;
        }
        &.primary {
            background: $newMainColor;
            &:focus {
                box-shadow:0px 0px 6px $newMainColor;
            }
        }
        &:after {
            left: 100%;
            top: 2px;
            margin-left: -26px;
        }
        .switcher-label {
            margin-left: 4px;
            .on-text {
                display: inline-block;
            }
            .off-text {
                display: none;
            }
        }
        .bk-switcher-loading {
            left: auto;
            right: 1px;
        }
    }
    &.bk-switcher-outline {
        background: $defaultColor;
        border: 1px solid $newGreyColor;
        &:after {
            width: 22px;
            height: 22px;
            box-shadow: none;
            background-color: $newGreyColor;
        }
        .switcher-label {
            height: 26px;
            color: $newGreyColor;
            margin-left: 24px;
        }
        &.is-checked {
            border: 1px solid $newGreenColor;
            &:after {
                margin-left: -24px;
                background-color: $newGreenColor;
            }
            .switcher-label {
                color: $newGreenColor;
                margin-left: 4px;
            }
        }
        &.is-disabled {
            background-color: $defaultColor !important;
            border-color: #eee !important;
            .switcher-label {
                color: #eee;
            }
            &:after {
                background-color: #eee;
            }
        }
        &.bk-switcher-large {
            &:after {
                width: 18px;
                height: 18px;
            }
            &.is-checked {
                &:after {
                    margin-left: -20px;
                }
            }
        }
    }
    &.bk-switcher-square {
        border-radius: 2px;
        &:after {
            border-radius: 2px;
        }
        .switcher-label {
            width: 35px;
        }
    }
    &.bk-switcher-large {
        width: 42px;
        height: 24px;
        line-height: 22px;
        border-radius: 12px;
        &:after {
            width: 20px;
            height: 20px;
        }
        &.is-checked {
            &:after {
                left: 100%;
                top: 2px;
                margin-left: -22px;
            }
        }
        .bk-switcher-loading {
            width: 22px;
        }
    }
    &.bk-switcher-small {
        width: 26px;
        height: 16px;
        line-height: 22px;
        &:after {
            width: 12px;
            height: 12px;
        }
        &.is-checked {
            &:after {
                left: 100%;
                top: 2px;
                margin-left: -14px;
            }
        }
        .bk-switcher-loading {
            width: 14px;
        }
    }
    input[type=checkbox],
    input[type=radio] {
        opacity: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1;
        cursor: pointer;
        left: 0;
        top: 0;
        margin: 0;
    }
}
