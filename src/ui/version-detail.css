@import "./mixins/scroller.css";
.bk-version {
    display: flex;
    margin: -23px -24px -16px;
    &-left {
        flex: 0 0 180px;
        background-color: #FAFBFD;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        position: relative;
        .left-fill {
            height: 20px;
            flex: 0 0 20px;
            border-right: 1px solid #DCDEE5;
            &.fill-top {
                margin-top: -10px;
            }
            &.fill-bottom {
                margin-bottom: -10px;
            }
        }
        .left-list {
            border-top: 1px solid #DCDEE5;
            border-bottom: 1px solid #DCDEE5;
            overflow-y: hidden;;
            display: flex;
            flex-direction: column;
            width: 100%;
            margin: 0;
            padding: 0;
            flex: 1;
            &-item {
                flex: 0 0 54px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding: 0 15px 0 30px;
                position: relative;
                border-bottom: 1px solid #DCDEE5;
                &:hover {
                    cursor: pointer;
                    background-color: #FFFFFF;
                }
                &.border-after {
                    &::after {
                        content: ' ';
                        width: 1px;
                        background: #DCDEE5;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        right: 0;
                    }
                }
                .item-title {
                    color: #63656E;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-weight: 600;
                }
                .item-date {
                    color: #979BA5;
                }
                .item-current {
                    position: absolute;
                    right: 20px;
                    top: 8px;
                    background-color: #699DF4;
                    border-radius: 2px;
                    width: 58px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #FFFFFF;
                }
                &.item-active {
                    &::before {
                        content: " ";
                        position: absolute;
                        top: 0px;
                        bottom: 0px;
                        left: 0;
                        width: 6px;
                        background-color: #3A84FF;
                    }
                    .item-title {
                        color: #313238;
                    }
                    .item-date {
                        color: #63656E;
                    }
                    background-color: #FFFFFF;
                }
            }
            &-fill {
                flex: 1;
                border-right: 1px solid #DCDEE5;
            }
            &-loading {
                flex: 0 0 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-right: 1px solid #DCDEE5;
                border-bottom: 1px solid #DCDEE5;
                .bk-loading-wrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            &:hover {
                overflow-y: auto;
                margin-right: -6px;
            }
            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
                background: rgba(0, 0, 0, .1);
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 6px;
                height: 100px;
                background: #C4C6CC;
                box-shadow: inset 0 0 6px rgba(204, 204, 204, 0.3);
            }
        }
        .resize-line {
            position: absolute;
            top: -10px;
            bottom: -10px;
            right: -6px;
            width: 13px;
            padding: 0 6px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            &:before, &:after {
                content: ' ';
                width: 0;
                height: 0;
                border: 5px solid transparent;
                margin: 0 0 0 1px;
            }
            .drag-content {
                flex: 1;
                margin: -5px 0;
                width: 1px;
                background-color: transparent;
            }
            &:hover, &:active {
                .drag-content {
                    background-color: #3A84FF;
                }
                &:before {
                    border-top-color: #3A84FF;
                }
                &:after {
                    border-bottom-color: #3A84FF;
                }
            }
        }
    }
    &-right {
        flex: 1;
        padding: 10px 30px 50px 45px;
        overflow: auto;
        @mixin scroller;
    }
    &-markdown-theme {
        font-size: 14px;
        color: #313238;
        h1,
        h2,
        h3,
        h4,
        h5 {
            height: auto;
            margin: 10px 0;
            font:
                normal 14px/1.5
                -apple-system,Helvetica Neue,Helvetica,Arial,PingFang SC,Microsoft YaHei,SimSun,sans-serif;
            font-weight: bold;
            color: #34383e;
        }
        h1 {
            font-size: 30px;
        }
        h2 {
            font-size: 24px;
        }
        h3 {
            font-size: 18px;
        }
        h4 {
            font-size: 16px;
        }
        h5 {
            font-size: 14px;
        }
        em {
            font-style: italic;
        }
        div,
        p,
        font,
        span,
        li {
            line-height: 1.3;
        }
        p {
            margin: 0 0 1em;
        }
        table,
        table p {
            margin: 0;
        }
        ul,
        ol {
            padding-left: 40px;
            margin: 10px 0 10px;
            text-indent: 0;
        }
        ul {
            list-style: disc;
            & > li {
                line-height: 1.8;
                white-space: normal;
                list-style: disc;
            }
        }
        ol {
            list-style: decimal;
            & > li {
                line-height: 1.8;
                white-space: normal;
                list-style: decimal;
            }
        }
        li > ul {
            margin-bottom: 10px;
        }
        li ol {
            padding-left: 20px !important;
        }
        ul ul,
        ul ol,
        ol ol,
        ol ul {
            margin-bottom: 0;
            margin-left: 20px;
        }
        pre,
        code {
            width: 95%;
            padding: 0 3px 2px;
            font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
            font-size: 14px;
            color: #333;
            border-radius: 3px;
        }
        code {
            padding: 2px 4px;
            font-family: Consolas, monospace, tahoma, Arial;
            color: #d14;
            border: 1px solid #e1e1e8;
        }
        pre {
            display: block;
            padding: 9.5px;
            margin: 0 0 10px;
            font-family: Consolas, monospace, tahoma, Arial;
            font-size: 13px;
            word-break: break-all;
            word-wrap: break-word;
            white-space: pre-wrap;
            background-color: #f6f6f6;
            border: 1px solid #ddd;
            border: 1px solid rgba(0,0,0,0.15);
            border-radius: 2px;
            code {
                padding: 0;
                white-space: pre-wrap;
                border: 0;
            }
        }
        blockquote {
            padding: 0 0 0 14px;
            margin: 0 0 20px;
            border-left: 5px solid #dfdfdf;
            ::before,
            ::after {
                content: "";
            }
            p {
                margin-bottom: 0;
                font-size: 14px;
                font-weight: 300;
                line-height: 25px;
            }
            small {
                display: block;
                line-height: 20px;
                color: #999;
                ::before {
                    content: "\2014 \00A0";
                }
            }
        }
    }
}
.version-detail-dialog .bk-dialog-tool .bk-dialog-close {
    right: 10px;
}