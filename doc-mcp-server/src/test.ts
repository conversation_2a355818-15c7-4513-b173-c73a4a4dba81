#!/usr/bin/env node

import { createBKUIDocServer } from "./server.js";
import path from "path";

async function testServer() {
  console.log("🧪 Testing BKUI Documentation MCP Server...\n");

  // 使用测试路径
  const componentsPath = path.resolve(process.cwd(), '../example/components');
  console.log(`Components path: ${componentsPath}\n`);

  const server = createBKUIDocServer(componentsPath);

  try {
    // 测试服务器信息
    console.log("📋 Server Info:");
    const serverInfo = server.getServerInfo();
    console.log(`  Name: ${serverInfo.name}`);
    console.log(`  Version: ${serverInfo.version || '1.0.0'}\n`);

    // 测试工具列表
    console.log("🔧 Available Tools:");
    const toolInfo = server.getToolListInfo();
    toolInfo.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // 测试执行工具
    console.log("🚀 Testing Tools:\n");

    // 测试 component-list
    console.log("1. Testing component-list...");
    try {
      const listResult = await server.getServerDetail().tools.componentList.execute({ includeDescription: true });
      console.log(`   ✅ Found ${listResult.total} components`);
      console.log(`   📝 First 3 components:`);
      listResult.components.slice(0, 3).forEach((comp: any) => {
        console.log(`      - ${comp.name} (${comp.displayName}): ${comp.description.substring(0, 50)}...`);
      });
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();

    // 测试 component-search
    console.log("2. Testing component-search...");
    try {
      const searchResult = await server.getServerDetail().tools.componentSearch.execute({ query: "button", limit: 3 });
      console.log(`   ✅ Found ${searchResult.total} components matching "button"`);
      searchResult.results.forEach((comp: any) => {
        console.log(`      - ${comp.name}: ${comp.description.substring(0, 50)}...`);
      });
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();

    // 测试 component-guide
    console.log("3. Testing component-guide...");
    try {
      const guideResult = await server.getServerDetail().tools.componentGuide.execute({ 
        componentName: "button", 
        sections: ["properties", "events"] 
      });
      console.log(`   ✅ Retrieved guide for ${guideResult.component.displayName}`);
      console.log(`   📋 Properties: ${guideResult.properties?.length || 0}`);
      console.log(`   🎯 Events: ${guideResult.events?.length || 0}`);
      
      if (guideResult.properties && guideResult.properties.length > 0) {
        console.log(`   📝 First property: ${guideResult.properties[0].name} (${guideResult.properties[0].type})`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();

    // 测试 component-examples
    console.log("4. Testing component-examples...");
    try {
      const examplesResult = await server.getServerDetail().tools.componentExamples.execute({ 
        componentName: "button" 
      });
      console.log(`   ✅ Found ${examplesResult.total} examples for ${examplesResult.componentName}`);
      examplesResult.examples.forEach((example: any, index: number) => {
        console.log(`      ${index + 1}. ${example.title} (${example.language})`);
      });
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();

    // 测试 component-props
    console.log("5. Testing component-props...");
    try {
      const propsResult = await server.getServerDetail().tools.componentProps.execute({ 
        componentName: "button" 
      });
      console.log(`   ✅ Found ${propsResult.total} properties for ${propsResult.componentName}`);
      propsResult.properties.slice(0, 3).forEach((prop: any) => {
        console.log(`      - ${prop.name}: ${prop.type} ${prop.required ? '(required)' : '(optional)'}`);
      });
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }

    console.log("\n✅ All tests completed!");

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await server.close();
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testServer().catch(console.error);
}
