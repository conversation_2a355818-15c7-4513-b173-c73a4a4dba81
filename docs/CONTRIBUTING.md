Contributing to bkui-vue2

我们欢迎 report Issues 或者 pull requests。 在贡献代码之前请阅读以下指引。

问题管理
我们用 Github Issues 去跟踪 public bugs 和 feature requests。

查找已知的 issue 优先
请查找已存在或者相类似的 issue，从而保证不存在冗余。

新建 Issues
新建 issues 时请提供详细的描述、截屏或者短视频来辅助我们定位问题

分支管理
Pull Requests
我们欢迎大家贡献代码来使我们的 bkui-vue2 更加强大，代码团队会监控所有的 pull request, 我们会做相应的代码检查和测试，测试通过之后我们就会接纳 PR ，但是不会立即合并到 master 分支。

在完成一个 pr 之前请做一下确认:

从 master 或者 staging fork 你自己的分支。
在修改了代码之后请修改对应的文档和注释。
在新建的文件中请加入 license 和 copy right 申明。
确保一致的代码风格。
做充分的测试。
然后，你可以提交你的代码到 dev 或者 hotfix 分支。
代码协议
MIT LICENSE 为 bkui-vue2 的开源协议，你贡献的代码也会受此协议保护。
