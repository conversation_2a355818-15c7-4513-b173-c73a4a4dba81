{"name": "lib_9c71e47775a31d72b663", "content": {"../node_modules/process/browser.js": {"id": "./node_modules/process/browser.js", "buildMeta": {"providedExports": true}}, "../node_modules/setimmediate/setImmediate.js": {"id": "./node_modules/setimmediate/setImmediate.js", "buildMeta": {"providedExports": true}}, "../node_modules/timers-browserify/main.js": {"id": "./node_modules/timers-browserify/main.js", "buildMeta": {"providedExports": true}}, "../node_modules/vue-i18n/dist/vue-i18n.esm.js": {"id": "./node_modules/vue-i18n/dist/vue-i18n.esm.js", "buildMeta": {"exportsType": "namespace", "providedExports": ["default"]}}, "../node_modules/vue-router/dist/vue-router.esm.js": {"id": "./node_modules/vue-router/dist/vue-router.esm.js", "buildMeta": {"exportsType": "namespace", "providedExports": ["default"]}}, "../node_modules/vue/dist/vue.esm.js": {"id": "./node_modules/vue/dist/vue.esm.js", "buildMeta": {"exportsType": "namespace", "providedExports": ["EffectScope", "computed", "customRef", "default", "defineAsyncComponent", "defineComponent", "del", "effectScope", "getCurrentInstance", "getCurrentScope", "h", "inject", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "isShallow", "mark<PERSON>aw", "mergeDefaults", "nextTick", "onActivated", "onBeforeMount", "onBeforeUnmount", "onBeforeUpdate", "onDeactivated", "onErrorCaptured", "onMounted", "onRenderTracked", "onRenderTriggered", "onScopeDispose", "onServerPrefetch", "onUnmounted", "onUpdated", "provide", "proxyRefs", "reactive", "readonly", "ref", "set", "shallowReactive", "shallowReadonly", "shallowRef", "toRaw", "toRef", "toRefs", "triggerRef", "unref", "useAttrs", "useCssModule", "useCssVars", "useListeners", "useSlots", "version", "watch", "watchEffect", "watchPostEffect", "watchSyncEffect"]}}, "../node_modules/webpack/buildin/global.js": {"id": "./node_modules/webpack/buildin/global.js", "buildMeta": {"providedExports": true}}}}